import { prisma } from '../server/db/prisma';
import { initDefaultFrameworks } from '../server/services/initFrameworks';

async function main() {
  try {
    console.log('开始初始化框架数据...');
    
    // 初始化默认框架
    await initDefaultFrameworks();
    
    // 检查框架数据
    const frameworks = await prisma.framework.findMany({
      orderBy: { sortOrder: 'asc' }
    });
    
    console.log('当前框架列表:');
    frameworks.forEach(framework => {
      console.log(`- ${framework.name} (${framework.status})`);
    });
    
    // 为现有的应用随机分配框架
    const applications = await prisma.application.findMany({
      where: { frameworkId: null }
    });
    
    if (applications.length > 0) {
      console.log(`\n为 ${applications.length} 个应用分配框架...`);
      
      const enabledFrameworks = frameworks.filter(f => f.status === 'ENABLED');
      
      for (const app of applications) {
        // 随机选择一个启用的框架
        const randomFramework = enabledFrameworks[Math.floor(Math.random() * enabledFrameworks.length)];
        
        await prisma.application.update({
          where: { id: app.id },
          data: { frameworkId: randomFramework.id }
        });
        
        console.log(`- ${app.name} -> ${randomFramework.name}`);
      }
    }
    
    console.log('\n✅ 框架数据初始化完成!');
  } catch (error) {
    console.error('❌ 初始化失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
