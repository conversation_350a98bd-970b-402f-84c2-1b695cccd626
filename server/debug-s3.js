import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function debugS3Config() {
  try {
    console.log('🔍 检查 S3 配置数据...')
    
    // 查询所有 S3 配置
    const configs = await prisma.s3Config.findMany({
      orderBy: { id: 'desc' }
    })
    
    console.log('📊 数据库中的 S3 配置数量:', configs.length)
    
    if (configs.length > 0) {
      console.log('📋 S3 配置详情:')
      configs.forEach((config, index) => {
        console.log(`\n配置 ${index + 1}:`)
        console.log('  ID:', config.id)
        console.log('  启用状态:', config.enabled)
        console.log('  Endpoint URL:', config.endpointUrl || '(空)')
        console.log('  Access Key ID:', config.awsAccessKeyId ? '***已设置***' : '(空)')
        console.log('  Secret Access Key:', config.awsSecretAccessKey ? '***已设置***' : '(空)')
        console.log('  Region:', config.regionName || '(空)')
        console.log('  Bucket Name:', config.bucketName || '(空)')
        console.log('  Folder:', config.folder || '(空)')
        console.log('  Bucket URL:', config.bucketUrl || '(空)')
        console.log('  创建时间:', config.createdAt)
        console.log('  更新时间:', config.updatedAt)
      })
    } else {
      console.log('❌ 数据库中没有找到 S3 配置')
    }
    
  } catch (error) {
    console.error('❌ 查询失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

debugS3Config()
