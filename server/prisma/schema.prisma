// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id        String   @id @default(cuid())
  username  String   @unique
  email     String   @unique
  password  String
  name      String
  avatar    String?
  googleId  String?  @unique @map("google_id") // Google OAuth ID
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  applications  Application[]
  conversations AiConversation[]

  @@map("users")
}

// 应用表 - 用户创建的开发项目
model Application {
  id          String            @id @default(cuid())
  name        String
  description String?
  userId      String
  frameworkId String?           // 关联的框架ID
  status      ApplicationStatus @default(CREATING)
  config      Json?             // 应用配置信息
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  // 关联关系
  user          User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  framework     Framework?       @relation(fields: [frameworkId], references: [id], onDelete: SetNull)
  container     Container?
  conversations AiConversation[]
  codeChanges   CodeChange[]

  @@map("applications")
}

// 容器表 - 每个应用对应的开发环境
model Container {
  id            String          @id @default(cuid())
  applicationId String          @unique
  dockerId      String?         @unique // Docker容器ID
  status        ContainerStatus @default(CREATING)
  config        Json            // 容器配置
  ports         Json            // 端口映射
  databaseUrl   String?         // 容器内数据库连接
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt

  // 关联关系
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@map("containers")
}

// AI对话记录表
model AiConversation {
  id            String   @id @default(cuid())
  applicationId String
  userId        String
  userMessage   String   @db.Text
  aiResponse    String   @db.Text
  messageType   String   @default("chat") // chat, code_request, code_review等
  metadata      Json?    // 额外的元数据
  createdAt     DateTime @default(now())

  // 关联关系
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ai_conversations")
}

// 代码变更记录表
model CodeChange {
  id            String   @id @default(cuid())
  applicationId String
  filePath      String   // 文件路径
  changeType    String   // create, update, delete
  oldContent    String?  @db.Text
  newContent    String?  @db.Text
  description   String?  // 变更描述
  conversationId String? // 关联的对话ID
  createdAt     DateTime @default(now())

  // 关联关系
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@map("code_changes")
}

// 枚举类型
enum UserRole {
  ADMIN
  USER
}

enum ApplicationStatus {
  CREATING    // 创建中
  ACTIVE      // 运行中
  STOPPED     // 已停止
  ERROR       // 错误状态
  DELETED     // 已删除
}

enum ContainerStatus {
  CREATING    // 创建中
  RUNNING     // 运行中
  STOPPED     // 已停止
  ERROR       // 错误状态
  DELETED     // 已删除
}

// 框架状态枚举
enum FrameworkStatus {
  ENABLED     // 启用
  COMING_SOON // 即将推出
  DISABLED    // 禁用
}

// 菜单表 (为了兼容现有的管理后台)
model Menu {
  id        Int      @id @default(autoincrement())
  parentId  Int?     @map("parent_id")
  title     String
  path      String?
  icon      String?
  sortOrder Int      @default(0) @map("sort_order")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 自关联关系
  parent   Menu?  @relation("MenuHierarchy", fields: [parentId], references: [id], onDelete: Cascade)
  children Menu[] @relation("MenuHierarchy")

  @@map("menus")
}

// 系统配置表 (为了兼容现有的管理后台)
model SystemConfig {
  id          Int      @id @default(autoincrement())
  configKey   String   @unique @map("config_key")
  configValue String?  @map("config_value")
  configType  String   @default("string") @map("config_type")
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("system_config")
}

// 开发框架表
model Framework {
  id          String          @id @default(cuid())
  name        String          @unique // 框架名称
  description String?         // 框架描述
  status      FrameworkStatus @default(ENABLED) // 框架状态
  introduction String?        // 框架介绍（可用于视频链接等）
  sortOrder   Int             @default(0) // 排序权重
  createdAt   DateTime        @default(now()) @map("created_at")
  updatedAt   DateTime        @updatedAt @map("updated_at")

  // 关联关系
  applications Application[]   // 使用此框架的应用

  @@map("frameworks")
}

// S3配置表 (为了兼容现有的管理后台)
model S3Config {
  id                  Int      @id @default(autoincrement())
  enabled             Boolean  @default(true)
  endpointUrl         String?  @map("endpoint_url")
  awsAccessKeyId      String   @map("aws_access_key_id")
  awsSecretAccessKey  String   @map("aws_secret_access_key")
  regionName          String   @map("region_name")
  bucketName          String   @map("bucket_name")
  folder              String?
  bucketUrl           String?  @map("bucket_url")
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  @@map("s3_config")
}
