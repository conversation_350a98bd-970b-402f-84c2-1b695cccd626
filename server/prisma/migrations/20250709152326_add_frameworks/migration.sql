-- CreateEnum
CREATE TYPE "FrameworkStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'DEPRECATED');

-- AlterTable
ALTER TABLE "applications" ADD COLUMN     "frameworkId" TEXT;

-- CreateTable
CREATE TABLE "frameworks" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "description" TEXT,
    "category" TEXT NOT NULL,
    "version" TEXT NOT NULL,
    "logo" TEXT,
    "status" "FrameworkStatus" NOT NULL DEFAULT 'ACTIVE',
    "config" JSONB NOT NULL,
    "template" JSONB NOT NULL,
    "dependencies" JSONB NOT NULL,
    "scripts" JSONB NOT NULL,
    "dockerConfig" JSONB NOT NULL,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "featured" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "frameworks_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "frameworks_name_key" ON "frameworks"("name");

-- CreateIndex
CREATE UNIQUE INDEX "frameworks_slug_key" ON "frameworks"("slug");

-- AddForeignKey
ALTER TABLE "applications" ADD CONSTRAINT "applications_frameworkId_fkey" FOREIGN KEY ("frameworkId") REFERENCES "frameworks"("id") ON DELETE SET NULL ON UPDATE CASCADE;
