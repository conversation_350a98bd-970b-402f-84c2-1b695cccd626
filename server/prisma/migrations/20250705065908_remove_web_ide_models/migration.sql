/*
  Warnings:

  - You are about to drop the `ai_conversations` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `containers` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `deployments` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `projects` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "ai_conversations" DROP CONSTRAINT "ai_conversations_projectId_fkey";

-- DropForeignKey
ALTER TABLE "ai_conversations" DROP CONSTRAINT "ai_conversations_userId_fkey";

-- DropForeignKey
ALTER TABLE "containers" DROP CONSTRAINT "containers_projectId_fkey";

-- DropForeignKey
ALTER TABLE "deployments" DROP CONSTRAINT "deployments_projectId_fkey";

-- DropForeignKey
ALTER TABLE "projects" DROP CONSTRAINT "projects_userId_fkey";

-- DropTable
DROP TABLE "ai_conversations";

-- DropTable
DROP TABLE "containers";

-- DropTable
DROP TABLE "deployments";

-- DropTable
DROP TABLE "projects";

-- DropEnum
DROP TYPE "ContainerStatus";

-- DropEnum
DROP TYPE "DeploymentStatus";

-- DropEnum
DROP TYPE "ProjectStatus";
