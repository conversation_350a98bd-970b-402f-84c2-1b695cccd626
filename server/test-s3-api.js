// 测试 S3 API 端点
async function testS3Api() {
  try {
    console.log('🔍 测试 S3 API 端点...')
    
    const response = await fetch('http://localhost:3000/api/s3/config', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiJ9.eyJpZCI6ImNtZHUwcXJ2MzAwMDAxNjN3OTdsYXVyM3QiLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6IkFETUlOIiwiaWF0IjoxNzU1MDIyNDEyLCJleHAiOjE3NTUxMDg4MTJ9.9c451RUsy1bRsLCZBh2VBuw_5YHuwJiGAhx-JQzG2DI'
      }
    })
    
    console.log('📊 响应状态:', response.status)
    console.log('📊 响应头:', Object.fromEntries(response.headers.entries()))
    
    const data = await response.text()
    console.log('📋 响应内容 (原始):', data)
    
    try {
      const jsonData = JSON.parse(data)
      console.log('📋 响应内容 (JSON):', JSON.stringify(jsonData, null, 2))
    } catch (e) {
      console.log('❌ 无法解析为 JSON')
    }
    
  } catch (error) {
    console.error('❌ API 测试失败:', error)
  }
}

testS3Api()
