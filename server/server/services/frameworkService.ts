import { prisma } from '../db/prisma';
import { logger } from '../utils/logger';
import type { FrameworkStatus } from '@prisma/client';

// 框架创建数据接口
export interface CreateFrameworkData {
  name: string;
  description?: string;
  status?: FrameworkStatus;
  introduction?: string;
  sortOrder?: number;
}

// 框架更新数据接口
export interface UpdateFrameworkData {
  name?: string;
  description?: string;
  status?: FrameworkStatus;
  introduction?: string;
  sortOrder?: number;
}

// 框架查询参数
export interface FrameworkQueryParams {
  page?: number;
  pageSize?: number;
  status?: FrameworkStatus;
  search?: string;
}

/**
 * 获取框架列表（分页）
 */
export async function getFrameworks(params: FrameworkQueryParams = {}) {
  try {
    const {
      page = 1,
      pageSize = 10,
      status,
      search
    } = params;

    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: any = {};
    
    if (status) {
      where.status = status;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }

    // 获取总数
    const total = await prisma.framework.count({ where });

    // 获取数据
    const frameworks = await prisma.framework.findMany({
      where,
      orderBy: [
        { sortOrder: 'asc' },
        { createdAt: 'desc' }
      ],
      skip,
      take: pageSize
    });

    const totalPages = Math.ceil(total / pageSize);

    logger.debug(`获取框架列表成功`, { 
      total, 
      page, 
      pageSize, 
      totalPages,
      status,
      search 
    });

    return {
      data: frameworks,
      pagination: {
        page,
        pageSize,
        total,
        totalPages
      }
    };
  } catch (error) {
    logger.error('获取框架列表失败', { error, params });
    throw new Error('获取框架列表失败');
  }
}

/**
 * 根据ID获取框架详情
 */
export async function getFrameworkById(id: string) {
  try {
    const framework = await prisma.framework.findUnique({
      where: { id }
    });

    if (!framework) {
      throw new Error('框架不存在');
    }

    logger.debug('获取框架详情成功', { frameworkId: id });
    return framework;
  } catch (error) {
    logger.error('获取框架详情失败', { error, frameworkId: id });
    throw error;
  }
}

/**
 * 创建新框架
 */
export async function createFramework(data: CreateFrameworkData) {
  try {
    // 检查名称是否已存在
    const existingFramework = await prisma.framework.findUnique({
      where: { name: data.name }
    });

    if (existingFramework) {
      throw new Error('框架名称已存在');
    }

    const framework = await prisma.framework.create({
      data: {
        name: data.name,
        description: data.description,
        status: data.status || ('ENABLED' as FrameworkStatus),
        introduction: data.introduction,
        sortOrder: data.sortOrder || 0
      }
    });

    logger.info('创建框架成功', { 
      frameworkId: framework.id, 
      name: framework.name 
    });
    
    return framework;
  } catch (error) {
    logger.error('创建框架失败', { error, data });
    throw error;
  }
}

/**
 * 更新框架
 */
export async function updateFramework(id: string, data: UpdateFrameworkData) {
  try {
    // 检查框架是否存在
    const existingFramework = await prisma.framework.findUnique({
      where: { id }
    });

    if (!existingFramework) {
      throw new Error('框架不存在');
    }

    // 如果更新名称，检查是否冲突
    if (data.name && data.name !== existingFramework.name) {
      const nameConflict = await prisma.framework.findUnique({
        where: { name: data.name }
      });

      if (nameConflict) {
        throw new Error('框架名称已存在');
      }
    }

    const framework = await prisma.framework.update({
      where: { id },
      data
    });

    logger.info('更新框架成功', { frameworkId: id });
    return framework;
  } catch (error) {
    logger.error('更新框架失败', { error, frameworkId: id, data });
    throw error;
  }
}

/**
 * 删除框架
 */
export async function deleteFramework(id: string) {
  try {
    // 检查框架是否存在
    const existingFramework = await prisma.framework.findUnique({
      where: { id }
    });

    if (!existingFramework) {
      throw new Error('框架不存在');
    }

    await prisma.framework.delete({
      where: { id }
    });

    logger.info('删除框架成功', { frameworkId: id, name: existingFramework.name });
    return true;
  } catch (error) {
    logger.error('删除框架失败', { error, frameworkId: id });
    throw error;
  }
}

/**
 * 获取框架统计信息
 */
export async function getFrameworkStats() {
  try {
    const [total, enabled, comingSoon, disabled] = await Promise.all([
      prisma.framework.count(),
      prisma.framework.count({ where: { status: 'ENABLED' } }),
      prisma.framework.count({ where: { status: 'COMING_SOON' } }),
      prisma.framework.count({ where: { status: 'DISABLED' } })
    ]);

    const stats = {
      total,
      enabled,
      comingSoon,
      disabled
    };

    logger.debug('获取框架统计成功', stats);
    return stats;
  } catch (error) {
    logger.error('获取框架统计失败', { error });
    throw new Error('获取框架统计失败');
  }
}

/**
 * 批量更新框架排序
 */
export async function updateFrameworksOrder(orders: { id: string; sortOrder: number }[]) {
  try {
    const updatePromises = orders.map(({ id, sortOrder }) =>
      prisma.framework.update({
        where: { id },
        data: { sortOrder }
      })
    );

    await Promise.all(updatePromises);

    logger.info('批量更新框架排序成功', { count: orders.length });
    return true;
  } catch (error) {
    logger.error('批量更新框架排序失败', { error, orders });
    throw new Error('更新框架排序失败');
  }
}

/**
 * 获取公开的框架列表 (供前端web调用)
 */
export async function getPublicFrameworks(status?: FrameworkStatus) {
  try {
    // 构建查询条件
    const where: any = {};

    if (status) {
      where.status = status;
    } else {
      // 默认只返回启用的框架
      where.status = 'ENABLED';
    }

    // 获取框架数据，按排序权重排序
    const frameworks = await prisma.framework.findMany({
      where,
      orderBy: [
        { sortOrder: 'asc' },
        { createdAt: 'desc' }
      ],
      select: {
        id: true,
        name: true,
        description: true,
        status: true,
        introduction: true,
        sortOrder: true
      }
    });

    logger.debug(`获取公开框架列表成功`, {
      count: frameworks.length,
      status
    });

    return frameworks;
  } catch (error) {
    logger.error('获取公开框架列表失败', { error, status });
    throw new Error('获取框架列表失败');
  }
}
