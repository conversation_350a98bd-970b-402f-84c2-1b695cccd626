import { prisma } from '../db/prisma';
import { hashPassword, verifyPassword, validatePasswordStrength } from '../utils/crypto';
import { ValidationError, NotFoundError } from '../utils/errors';
import type { User, UserRole } from '@prisma/client';

// 用户登录参数
export interface LoginParams {
  username: string;
  password: string;
}

// 用户创建参数
export interface CreateUserParams {
  username: string;
  password: string;
  name: string;
  role: UserRole;
  email?: string;
  avatar?: string;
}

// 用户更新参数
export interface UpdateUserParams {
  name?: string;
  role?: UserRole;
  email?: string;
  avatar?: string;
  password?: string;
}

// 筛选查询参数
export interface UserFilterParams {
  search?: string;
  role?: UserRole;
}

// 分页参数
export interface UserListParams {
  page?: number;
  pageSize?: number;
  search?: string;
  role?: UserRole;
}

// 分页响应
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * 获取所有用户
 */
export async function getAllUsers(): Promise<Omit<User, 'password'>[]> {
  return await prisma.user.findMany({
    orderBy: { createdAt: 'desc' },
    select: {
      id: true,
      username: true,
      email: true,
      name: true,
      role: true,
      avatar: true,
      googleId: true,
      createdAt: true,
      updatedAt: true,
      // 明确排除密码字段
      password: false
    }
  });
}

/**
 * 根据ID获取用户
 */
export async function getUserById(id: string): Promise<Omit<User, 'password'> | null> {
  return await prisma.user.findUnique({
    where: { id },
    select: {
      id: true,
      username: true,
      email: true,
      name: true,
      role: true,
      avatar: true,
      googleId: true,
      createdAt: true,
      updatedAt: true,
      // 明确排除密码字段
      password: false
    }
  });
}

/**
 * 创建用户
 */
export async function createUser(params: CreateUserParams): Promise<User> {
  const { username, password, name, role, email, avatar } = params;

  // 验证密码强度
  if (!validatePasswordStrength(password)) {
    throw new ValidationError('密码强度不足，至少需要8位字符，包含大小写字母、数字和特殊字符');
  }

  // 检查用户名是否已存在
  const existingUser = await prisma.user.findUnique({
    where: { username }
  });

  if (existingUser) {
    throw new ValidationError('用户名已存在');
  }

  // 检查邮箱是否已存在
  if (email) {
    const existingEmail = await prisma.user.findUnique({
      where: { email }
    });

    if (existingEmail) {
      throw new ValidationError('邮箱已存在');
    }
  }

  // 加密密码
  const hashedPassword = await hashPassword(password);

  // 创建用户
  return await prisma.user.create({
    data: {
      username,
      password: hashedPassword,
      name,
      role,
      email: email || '',
      avatar
    }
  });
}

/**
 * 更新用户
 */
export async function updateUser(id: string, params: UpdateUserParams): Promise<User> {
  const { name, role, email, avatar, password } = params;

  // 检查用户是否存在
  const existingUser = await prisma.user.findUnique({
    where: { id }
  });

  if (!existingUser) {
    throw new NotFoundError('用户不存在');
  }

  // 检查邮箱是否已被其他用户使用
  if (email && email !== existingUser.email) {
    const existingEmail = await prisma.user.findUnique({
      where: { email }
    });

    if (existingEmail && existingEmail.id !== id) {
      throw new ValidationError('邮箱已被其他用户使用');
    }
  }

  // 准备更新数据
  const updateData: any = {
    name,
    role,
    email,
    avatar,
    updatedAt: new Date()
  };

  // 如果需要更新密码
  if (password) {
    if (!validatePasswordStrength(password)) {
      throw new ValidationError('密码强度不足，至少需要8位字符，包含大小写字母、数字和特殊字符');
    }
    updateData.password = await hashPassword(password);
  }

  // 移除 undefined 值
  Object.keys(updateData).forEach(key => {
    if (updateData[key] === undefined) {
      delete updateData[key];
    }
  });

  return await prisma.user.update({
    where: { id },
    data: updateData
  });
}

/**
 * 删除用户
 */
export async function deleteUser(id: string): Promise<void> {
  const existingUser = await prisma.user.findUnique({
    where: { id }
  });

  if (!existingUser) {
    throw new NotFoundError('用户不存在');
  }

  await prisma.user.delete({
    where: { id }
  });
}

/**
 * 验证用户登录
 */
export async function validateUser(username: string, password: string): Promise<User | null> {
  const user = await prisma.user.findUnique({
    where: { username }
  });

  if (!user) {
    return null;
  }

  const isValid = await verifyPassword(password, user.password);
  if (!isValid) {
    return null;
  }

  return user;
}

/**
 * 获取筛选后的用户列表
 */
export async function getFilteredUsers(params: UserFilterParams): Promise<Omit<User, 'password'>[]> {
  const { search, role } = params;

  const where: any = {};

  if (search) {
    where.OR = [
      { username: { contains: search, mode: 'insensitive' } },
      { name: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } }
    ];
  }

  if (role) {
    where.role = role;
  }

  return await prisma.user.findMany({
    where,
    orderBy: { createdAt: 'desc' },
    select: {
      id: true,
      username: true,
      email: true,
      name: true,
      role: true,
      avatar: true,
      googleId: true,
      createdAt: true,
      updatedAt: true,
      // 明确排除密码字段
      password: false
    }
  });
}

/**
 * 获取分页用户列表
 */
export async function getUsersPaginated(params: UserListParams): Promise<PaginatedResponse<Omit<User, 'password'>>> {
  const { page = 1, pageSize = 10, search, role } = params;

  const where: any = {};

  if (search) {
    where.OR = [
      { username: { contains: search, mode: 'insensitive' } },
      { name: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } }
    ];
  }

  if (role) {
    where.role = role;
  }

  const [users, total] = await Promise.all([
    prisma.user.findMany({
      where,
      skip: (page - 1) * pageSize,
      take: pageSize,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        role: true,
        avatar: true,
        googleId: true,
        createdAt: true,
        updatedAt: true,
        // 明确排除密码字段
        password: false
      }
    }),
    prisma.user.count({ where })
  ]);

  return {
    data: users,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  };
}
