import { logger } from '../utils/logger';
import { verifyToken } from '../utils/jwt';
import { processUserMessage, updateAiResponse } from './aiConversationService';
import { createCodeChange } from './codeChangeService';
import { getApplicationById } from './applicationService';

// WebSocket连接管理
interface WebSocketConnection {
  ws: any;
  userId: string;
  applicationId?: string;
  lastActivity: Date;
}

// 消息类型定义
interface WebSocketMessage {
  type: 'auth' | 'join_app' | 'chat' | 'code_change' | 'ping';
  data: any;
  messageId?: string;
}

// 响应消息类型
interface WebSocketResponse {
  type: 'auth_success' | 'auth_error' | 'joined_app' | 'chat_response' | 'code_update' | 'error' | 'pong';
  data?: any;
  messageId?: string;
  timestamp: string;
}

class WebSocketService {
  private connections = new Map<string, WebSocketConnection>();
  private applicationConnections = new Map<string, Set<string>>(); // applicationId -> Set<connectionId>

  /**
   * 处理新的WebSocket连接
   */
  handleConnection(ws: any, connectionId: string) {
    logger.info(`WebSocket连接建立: ${connectionId}`);

    // 设置连接超时
    const timeout = setTimeout(() => {
      if (this.connections.has(connectionId)) {
        logger.warn(`WebSocket连接超时未认证: ${connectionId}`);
        ws.close(1008, 'Authentication timeout');
      }
    }, 30000); // 30秒认证超时

    ws.on('message', async (message: string) => {
      try {
        const parsedMessage: WebSocketMessage = JSON.parse(message);
        await this.handleMessage(ws, connectionId, parsedMessage);
      } catch (error) {
        logger.error(`WebSocket消息解析失败: ${connectionId}`, {
          error: error instanceof Error ? error.message : String(error)
        });
        this.sendError(ws, 'Invalid message format');
      }
    });

    ws.on('close', () => {
      clearTimeout(timeout);
      this.handleDisconnection(connectionId);
    });

    ws.on('error', (error: any) => {
      logger.error(`WebSocket错误: ${connectionId}`, error);
      clearTimeout(timeout);
      this.handleDisconnection(connectionId);
    });
  }

  /**
   * 处理WebSocket消息
   */
  private async handleMessage(ws: any, connectionId: string, message: WebSocketMessage) {
    const connection = this.connections.get(connectionId);

    switch (message.type) {
      case 'auth':
        await this.handleAuth(ws, connectionId, message);
        break;

      case 'join_app':
        if (!connection?.userId) {
          this.sendError(ws, 'Not authenticated', message.messageId);
          return;
        }
        await this.handleJoinApplication(ws, connectionId, message);
        break;

      case 'chat':
        if (!connection?.userId || !connection?.applicationId) {
          this.sendError(ws, 'Not authenticated or not joined to application', message.messageId);
          return;
        }
        await this.handleChatMessage(ws, connectionId, message);
        break;

      case 'code_change':
        if (!connection?.userId || !connection?.applicationId) {
          this.sendError(ws, 'Not authenticated or not joined to application', message.messageId);
          return;
        }
        await this.handleCodeChange(ws, connectionId, message);
        break;

      case 'ping':
        this.sendResponse(ws, {
          type: 'pong',
          messageId: message.messageId,
          timestamp: new Date().toISOString()
        });
        break;

      default:
        this.sendError(ws, 'Unknown message type', message.messageId);
    }

    // 更新最后活动时间
    if (connection) {
      connection.lastActivity = new Date();
    }
  }

  /**
   * 处理用户认证
   */
  private async handleAuth(ws: any, connectionId: string, message: WebSocketMessage) {
    try {
      const { token } = message.data;
      
      if (!token) {
        this.sendError(ws, 'Token required', message.messageId);
        return;
      }

      const user = await verifyToken(token);
      if (!user) {
        this.sendError(ws, 'Invalid token', message.messageId);
        return;
      }

      // 保存连接信息
      this.connections.set(connectionId, {
        ws,
        userId: user.id,
        lastActivity: new Date()
      });

      this.sendResponse(ws, {
        type: 'auth_success',
        data: { userId: user.id, username: user.username },
        messageId: message.messageId,
        timestamp: new Date().toISOString()
      });

      logger.info(`WebSocket用户认证成功: ${connectionId} - ${user.username}`);
    } catch (error) {
      logger.error(`WebSocket认证失败: ${connectionId}`, {
        error: error instanceof Error ? error.message : String(error)
      });
      this.sendError(ws, 'Authentication failed', message.messageId);
    }
  }

  /**
   * 处理加入应用
   */
  private async handleJoinApplication(ws: any, connectionId: string, message: WebSocketMessage) {
    try {
      const { applicationId } = message.data;
      const connection = this.connections.get(connectionId);
      
      if (!connection) return;

      // 检查应用权限
      const application = await getApplicationById(applicationId);
      if (!application || application.userId !== connection.userId) {
        this.sendError(ws, 'Application not found or access denied', message.messageId);
        return;
      }

      // 更新连接信息
      connection.applicationId = applicationId;

      // 添加到应用连接组
      if (!this.applicationConnections.has(applicationId)) {
        this.applicationConnections.set(applicationId, new Set());
      }
      this.applicationConnections.get(applicationId)!.add(connectionId);

      this.sendResponse(ws, {
        type: 'joined_app',
        data: { applicationId, applicationName: application.name },
        messageId: message.messageId,
        timestamp: new Date().toISOString()
      });

      logger.info(`用户加入应用: ${connection.userId} -> ${applicationId}`);
    } catch (error) {
      logger.error(`加入应用失败: ${connectionId}`, {
        error: error instanceof Error ? error.message : String(error)
      });
      this.sendError(ws, 'Failed to join application', message.messageId);
    }
  }

  /**
   * 处理聊天消息
   */
  private async handleChatMessage(ws: any, connectionId: string, message: WebSocketMessage) {
    try {
      const connection = this.connections.get(connectionId);
      if (!connection) return;

      const { userMessage, messageType = 'chat' } = message.data;

      // 创建对话记录
      const conversation = await processUserMessage(
        connection.applicationId!,
        connection.userId,
        userMessage,
        messageType
      );

      // 立即返回对话ID
      this.sendResponse(ws, {
        type: 'chat_response',
        data: {
          conversationId: conversation.id,
          status: 'processing',
          userMessage: conversation.userMessage
        },
        messageId: message.messageId,
        timestamp: new Date().toISOString()
      });

      // 模拟AI处理完成后的响应（实际项目中这会是AI服务的回调）
      setTimeout(async () => {
        try {
          // 获取更新后的对话（包含AI响应）
          const updatedConversation = await updateAiResponse({
            conversationId: conversation.id,
            aiResponse: `AI回复: ${userMessage}`,
            metadata: { processed: true }
          });

          // 广播AI响应给应用内的所有连接
          this.broadcastToApplication(connection.applicationId!, {
            type: 'chat_response',
            data: {
              conversationId: updatedConversation.id,
              status: 'completed',
              aiResponse: updatedConversation.aiResponse,
              metadata: updatedConversation.metadata
            },
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          logger.error(`AI响应处理失败: ${conversation.id}`, {
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }, 2000);

    } catch (error) {
      logger.error(`聊天消息处理失败: ${connectionId}`, {
        error: error instanceof Error ? error.message : String(error)
      });
      this.sendError(ws, 'Failed to process chat message', message.messageId);
    }
  }

  /**
   * 处理代码变更
   */
  private async handleCodeChange(ws: any, connectionId: string, message: WebSocketMessage) {
    try {
      const connection = this.connections.get(connectionId);
      if (!connection) return;

      const { filePath, changeType, oldContent, newContent, description } = message.data;

      // 记录代码变更
      const codeChange = await createCodeChange({
        applicationId: connection.applicationId!,
        filePath,
        changeType,
        oldContent,
        newContent,
        description
      });

      // 广播代码变更给应用内的所有连接
      this.broadcastToApplication(connection.applicationId!, {
        type: 'code_update',
        data: {
          codeChangeId: codeChange.id,
          filePath: codeChange.filePath,
          changeType: codeChange.changeType,
          description: codeChange.description,
          userId: connection.userId
        },
        timestamp: new Date().toISOString()
      });

      logger.info(`代码变更记录: ${codeChange.id} - ${filePath}`);
    } catch (error) {
      logger.error(`代码变更处理失败: ${connectionId}`, {
        error: error instanceof Error ? error.message : String(error)
      });
      this.sendError(ws, 'Failed to process code change', message.messageId);
    }
  }

  /**
   * 处理连接断开
   */
  private handleDisconnection(connectionId: string) {
    const connection = this.connections.get(connectionId);
    
    if (connection) {
      // 从应用连接组中移除
      if (connection.applicationId) {
        const appConnections = this.applicationConnections.get(connection.applicationId);
        if (appConnections) {
          appConnections.delete(connectionId);
          if (appConnections.size === 0) {
            this.applicationConnections.delete(connection.applicationId);
          }
        }
      }

      this.connections.delete(connectionId);
      logger.info(`WebSocket连接断开: ${connectionId} - ${connection.userId}`);
    }
  }

  /**
   * 向应用内所有连接广播消息
   */
  private broadcastToApplication(applicationId: string, response: WebSocketResponse) {
    const connections = this.applicationConnections.get(applicationId);
    if (!connections) return;

    connections.forEach(connectionId => {
      const connection = this.connections.get(connectionId);
      if (connection) {
        this.sendResponse(connection.ws, response);
      }
    });
  }

  /**
   * 发送响应消息
   */
  private sendResponse(ws: any, response: WebSocketResponse) {
    try {
      ws.send(JSON.stringify(response));
    } catch (error) {
      logger.error('发送WebSocket响应失败:', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 发送错误消息
   */
  private sendError(ws: any, error: string, messageId?: string) {
    this.sendResponse(ws, {
      type: 'error',
      data: { error },
      messageId,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 清理不活跃的连接
   */
  cleanupInactiveConnections() {
    const now = new Date();
    const timeout = 30 * 60 * 1000; // 30分钟超时

    this.connections.forEach((connection, connectionId) => {
      if (now.getTime() - connection.lastActivity.getTime() > timeout) {
        logger.info(`清理不活跃连接: ${connectionId}`);
        connection.ws.close(1000, 'Inactive connection');
        this.handleDisconnection(connectionId);
      }
    });
  }

  /**
   * 获取连接统计
   */
  getStats() {
    return {
      totalConnections: this.connections.size,
      applicationConnections: Array.from(this.applicationConnections.entries()).map(([appId, connections]) => ({
        applicationId: appId,
        connectionCount: connections.size
      }))
    };
  }
}

export const websocketService = new WebSocketService();

// 定期清理不活跃连接 - 减少频率以降低内存压力
setInterval(() => {
  try {
    websocketService.cleanupInactiveConnections();
  } catch (error) {
    logger.error('WebSocket清理失败:', {
      error: error instanceof Error ? error.message : String(error)
    });
  }
}, 10 * 60 * 1000); // 每10分钟检查一次
