import { prisma } from '../db/prisma';
import { logger } from '../utils/logger';
import type { AiConversation } from '@prisma/client';

// 对话创建参数
export interface CreateConversationParams {
  applicationId: string;
  userId: string;
  userMessage: string;
  messageType?: string;
  metadata?: any;
}

// 对话查询参数
export interface ConversationListParams {
  applicationId: string;
  userId?: string;
  messageType?: string;
  page?: number;
  pageSize?: number;
}

// AI响应参数
export interface AiResponseParams {
  conversationId: string;
  aiResponse: string;
  metadata?: any;
}

/**
 * 创建新的对话记录
 */
export async function createConversation(params: CreateConversationParams): Promise<AiConversation> {
  try {
    const conversation = await prisma.aiConversation.create({
      data: {
        applicationId: params.applicationId,
        userId: params.userId,
        userMessage: params.userMessage,
        aiResponse: '', // 初始为空，等待AI响应
        messageType: params.messageType || 'chat',
        metadata: params.metadata || {}
      }
    });

    logger.info(`对话创建成功: ${conversation.id}`, { 
      conversationId: conversation.id, 
      applicationId: params.applicationId 
    });

    return conversation;
  } catch (error) {
    logger.error("创建对话失败:", error as Record<string, any>);
    throw new Error("创建对话失败");
  }
}

/**
 * 更新AI响应
 */
export async function updateAiResponse(params: AiResponseParams): Promise<AiConversation> {
  try {
    const conversation = await prisma.aiConversation.update({
      where: { id: params.conversationId },
      data: {
        aiResponse: params.aiResponse,
        metadata: params.metadata
      }
    });

    logger.info(`AI响应更新成功: ${params.conversationId}`);
    return conversation;
  } catch (error) {
    logger.error("更新AI响应失败:", error as Record<string, any>);
    throw new Error("更新AI响应失败");
  }
}

/**
 * 获取应用的对话历史
 */
export async function getConversationHistory(params: ConversationListParams) {
  try {
    const { applicationId, userId, messageType, page = 1, pageSize = 50 } = params;

    const where: any = { applicationId };
    if (userId) where.userId = userId;
    if (messageType) where.messageType = messageType;

    const [conversations, total] = await Promise.all([
      prisma.aiConversation.findMany({
        where,
        include: {
          user: {
            select: { id: true, username: true, name: true }
          }
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: { createdAt: 'asc' } // 按时间顺序排列
      }),
      prisma.aiConversation.count({ where })
    ]);

    return {
      data: conversations,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  } catch (error) {
    logger.error("获取对话历史失败:", error as Record<string, any>);
    throw new Error("获取对话历史失败");
  }
}

/**
 * 获取最近的对话
 */
export async function getRecentConversations(applicationId: string, limit: number = 10): Promise<AiConversation[]> {
  try {
    return await prisma.aiConversation.findMany({
      where: { applicationId },
      include: {
        user: {
          select: { id: true, username: true, name: true }
        }
      },
      take: limit,
      orderBy: { createdAt: 'desc' }
    });
  } catch (error) {
    logger.error("获取最近对话失败:", error as Record<string, any>);
    throw new Error("获取最近对话失败");
  }
}

/**
 * 删除对话
 */
export async function deleteConversation(id: string): Promise<void> {
  try {
    await prisma.aiConversation.delete({
      where: { id }
    });

    logger.info(`对话删除成功: ${id}`);
  } catch (error) {
    logger.error("删除对话失败:", error as Record<string, any>);
    throw new Error("删除对话失败");
  }
}

/**
 * 模拟AI处理用户消息
 * 实际项目中这里会调用真实的AI服务
 */
export async function processUserMessage(
  applicationId: string, 
  userId: string, 
  userMessage: string,
  messageType: string = 'chat'
): Promise<AiConversation> {
  try {
    // 创建对话记录
    const conversation = await createConversation({
      applicationId,
      userId,
      userMessage,
      messageType
    });

    // 模拟AI处理（实际项目中这里会调用AI服务）
    setTimeout(async () => {
      try {
        let aiResponse = '';
        let metadata = {};

        // 根据消息类型生成不同的响应
        switch (messageType) {
          case 'code_request':
            aiResponse = `我理解您想要开发：${userMessage}\n\n我将为您生成相应的代码。请稍等...`;
            metadata = { 
              codeGenerated: true, 
              language: 'javascript',
              files: ['src/components/NewComponent.vue', 'src/api/newApi.js']
            };
            break;
          
          case 'code_review':
            aiResponse = `我已经审查了您的代码。以下是我的建议：\n\n1. 代码结构清晰\n2. 建议添加错误处理\n3. 可以优化性能`;
            metadata = { reviewCompleted: true, suggestions: 3 };
            break;
          
          default:
            aiResponse = `我收到了您的消息："${userMessage}"\n\n我正在分析您的需求，请告诉我更多细节，我可以帮您：\n- 生成代码\n- 修改现有功能\n- 解答技术问题`;
        }

        // 更新AI响应
        await updateAiResponse({
          conversationId: conversation.id,
          aiResponse,
          metadata
        });

        logger.info(`AI处理完成: ${conversation.id}`);
      } catch (error) {
        logger.error(`AI处理失败: ${conversation.id}`, error);
      }
    }, 1000 + Math.random() * 2000); // 模拟1-3秒的处理时间

    return conversation;
  } catch (error) {
    logger.error("处理用户消息失败:", error as Record<string, any>);
    throw new Error("处理用户消息失败");
  }
}

/**
 * 获取对话统计信息
 */
export async function getConversationStats(applicationId: string) {
  try {
    const [total, byType] = await Promise.all([
      prisma.aiConversation.count({
        where: { applicationId }
      }),
      prisma.aiConversation.groupBy({
        by: ['messageType'],
        where: { applicationId },
        _count: { messageType: true }
      })
    ]);

    return {
      total,
      byType: byType.reduce((acc, item) => {
        acc[item.messageType] = item._count.messageType;
        return acc;
      }, {} as Record<string, number>)
    };
  } catch (error) {
    logger.error("获取对话统计失败:", error as Record<string, any>);
    throw new Error("获取对话统计失败");
  }
}
