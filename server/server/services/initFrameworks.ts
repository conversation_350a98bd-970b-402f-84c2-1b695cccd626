import { prisma } from '../db/prisma';
import { logger } from '../utils/logger';

// 初始框架数据
const initialFrameworks = [
  {
    name: 'Next.js',
    description: '基于React的全栈框架，支持SSR、SSG和API路由',
    status: 'ENABLED' as const,
    introduction: 'Next.js是一个强大的React框架，提供了服务端渲染、静态站点生成、API路由等功能。适合构建现代化的Web应用程序。',
    sortOrder: 1
  },
  {
    name: 'Vue 3',
    description: '渐进式JavaScript框架，易学易用，性能出色',
    status: 'ENABLED' as const,
    introduction: 'Vue 3是一个渐进式的JavaScript框架，具有响应式数据绑定、组件化开发、虚拟DOM等特性。适合快速开发交互式用户界面。',
    sortOrder: 2
  },
  {
    name: 'React',
    description: '用于构建用户界面的JavaScript库',
    status: 'ENABLED' as const,
    introduction: 'React是Facebook开发的用于构建用户界面的JavaScript库，采用组件化开发模式，拥有丰富的生态系统。',
    sortOrder: 3
  },
  {
    name: 'NestJS',
    description: '基于Node.js的企业级后端框架，使用TypeScript构建',
    status: 'ENABLED' as const,
    introduction: 'NestJS是一个用于构建高效、可扩展的Node.js服务器端应用程序的框架。它使用TypeScript构建，并结合了OOP、FP和FRP的元素。',
    sortOrder: 4
  },
  {
    name: 'Express.js',
    description: '快速、极简的Node.js Web框架',
    status: 'ENABLED' as const,
    introduction: 'Express.js是一个简洁而灵活的Node.js Web应用框架，提供了一系列强大特性帮助你创建各种Web和移动设备应用。',
    sortOrder: 5
  },
  {
    name: 'Svelte',
    description: '编译时优化的前端框架，无虚拟DOM',
    status: 'COMING_SOON' as const,
    introduction: 'Svelte是一个构建用户界面的工具，它在构建时将你的组件转换为高效的原生JavaScript代码，无需虚拟DOM。',
    sortOrder: 6
  },
  {
    name: 'Angular',
    description: '由Google维护的企业级前端框架',
    status: 'COMING_SOON' as const,
    introduction: 'Angular是一个基于TypeScript的开源Web应用框架，由Google的Angular团队以及社区共同领导。',
    sortOrder: 7
  },
  {
    name: 'Django',
    description: 'Python高级Web框架，快速开发、简洁设计',
    status: 'COMING_SOON' as const,
    introduction: 'Django是一个开放源代码的Web应用框架，由Python写成。采用了MTV的框架模式，即模型M，视图V和模版T。',
    sortOrder: 8
  }
];

/**
 * 初始化默认框架数据
 */
export async function initDefaultFrameworks() {
  try {
    // 检查是否已有框架数据
    const existingFrameworks = await prisma.framework.count();
    
    if (existingFrameworks > 0) {
      logger.debug('框架数据已存在，跳过初始化');
      return;
    }

    // 创建初始框架数据
    for (const frameworkData of initialFrameworks) {
      await prisma.framework.create({
        data: frameworkData
      });
    }

    logger.info(`✅ 初始化 ${initialFrameworks.length} 个默认框架完成`);
  } catch (error) {
    logger.error('❌ 初始化默认框架失败:', {
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}
