import { prisma } from '../db/prisma';
import { logger } from '../utils/logger';
import type { Menu } from '@prisma/client';

// 菜单创建参数
export interface CreateMenuParams {
  title: string;
  path?: string;
  icon?: string;
  parentId?: number;
  sortOrder?: number;
}

// 菜单更新参数
export interface UpdateMenuParams {
  title?: string;
  path?: string;
  icon?: string;
  parentId?: number;
  sortOrder?: number;
}

// 带子菜单的菜单类型
export interface MenuWithChildren extends Menu {
  children?: MenuWithChildren[];
}

/**
 * 获取所有菜单
 */
export async function getAllMenus(): Promise<Menu[]> {
  try {
    return await prisma.menu.findMany({
      orderBy: { sortOrder: 'asc' }
    });
  } catch (error) {
    logger.error("获取菜单列表失败:", error as Record<string, any>);
    throw new Error("获取菜单列表失败");
  }
}

/**
 * 获取菜单树
 */
export async function getMenuTree(): Promise<MenuWithChildren[]> {
  try {
    const menus = await prisma.menu.findMany({
      orderBy: { sortOrder: 'asc' }
    });
    return buildMenuTree(menus);
  } catch (error) {
    logger.error("获取菜单树失败:", error as Record<string, any>);
    throw new Error("获取菜单树失败");
  }
}

/**
 * 构建菜单树
 */
function buildMenuTree(menus: Menu[], parentId: number | null = null): MenuWithChildren[] {
  return menus
    .filter(menu => menu.parentId === parentId)
    .map(menu => ({
      ...menu,
      children: buildMenuTree(menus, menu.id)
    }));
}

/**
 * 根据ID获取菜单
 */
export async function getMenuById(id: number): Promise<Menu | null> {
  try {
    return await prisma.menu.findUnique({
      where: { id }
    });
  } catch (error) {
    logger.error("获取菜单失败:", error as Record<string, any>);
    throw new Error("获取菜单失败");
  }
}

/**
 * 创建菜单
 */
export async function createMenu(params: CreateMenuParams): Promise<Menu> {
  try {
    return await prisma.menu.create({
      data: {
        title: params.title,
        path: params.path,
        icon: params.icon,
        parentId: params.parentId,
        sortOrder: params.sortOrder || 0
      }
    });
  } catch (error) {
    logger.error("创建菜单失败:", error as Record<string, any>);
    throw new Error("创建菜单失败");
  }
}

/**
 * 更新菜单
 */
export async function updateMenu(id: number, params: UpdateMenuParams): Promise<Menu> {
  try {
    // 检查菜单是否存在
    const existingMenu = await prisma.menu.findUnique({
      where: { id }
    });

    if (!existingMenu) {
      throw new Error("菜单不存在");
    }

    // 准备更新数据
    const updateData: any = {
      updatedAt: new Date()
    };

    if (params.title !== undefined) updateData.title = params.title;
    if (params.path !== undefined) updateData.path = params.path;
    if (params.icon !== undefined) updateData.icon = params.icon;
    if (params.parentId !== undefined) updateData.parentId = params.parentId;
    if (params.sortOrder !== undefined) updateData.sortOrder = params.sortOrder;

    return await prisma.menu.update({
      where: { id },
      data: updateData
    });
  } catch (error) {
    logger.error("更新菜单失败:", error as Record<string, any>);
    throw new Error("更新菜单失败");
  }
}

/**
 * 删除菜单
 */
export async function deleteMenu(id: number): Promise<void> {
  try {
    // 检查菜单是否存在
    const existingMenu = await prisma.menu.findUnique({
      where: { id }
    });

    if (!existingMenu) {
      throw new Error("菜单不存在");
    }

    // 检查是否有子菜单
    const childMenus = await prisma.menu.findMany({
      where: { parentId: id }
    });

    if (childMenus.length > 0) {
      throw new Error("请先删除子菜单");
    }

    await prisma.menu.delete({
      where: { id }
    });
  } catch (error) {
    logger.error("删除菜单失败:", error as Record<string, any>);
    throw new Error("删除菜单失败");
  }
}

/**
 * 根据角色获取菜单
 * 注意：这里简化处理，管理员获取所有菜单，普通用户获取基础菜单
 */
export async function getMenusByRole(role: string): Promise<MenuWithChildren[]> {
  try {
    if (role === 'ADMIN' || role === 'admin') {
      // 管理员获取所有菜单
      return await getMenuTree();
    } else {
      // 普通用户获取基础菜单（排除管理功能）
      const menus = await prisma.menu.findMany({
        where: {
          title: {
            notIn: ['用户管理', '菜单管理', '系统设置']
          }
        },
        orderBy: { sortOrder: 'asc' }
      });
      return buildMenuTree(menus);
    }
  } catch (error) {
    logger.error("获取角色菜单失败:", error as Record<string, any>);
    throw new Error("获取角色菜单失败");
  }
}

/**
 * 初始化默认菜单数据
 */
export async function initDefaultMenus(): Promise<void> {
  try {
    // 检查是否已有菜单数据
    const existingMenus = await prisma.menu.findMany();
    
    if (existingMenus.length === 0) {
      // 创建默认菜单
      const defaultMenus = [
        { id: 1, title: '仪表盘', path: '/dashboard', icon: 'LayoutDashboard', sortOrder: 0 },
        { id: 2, title: '用户管理', path: '/userManager', icon: 'Users', sortOrder: 1 },
        { id: 3, title: '文件管理', path: '/files', icon: 'FolderOpen', sortOrder: 2 },
        { id: 4, title: '菜单管理', path: '/menusManager', icon: 'Menu', sortOrder: 3 },
        { id: 5, title: '框架管理', path: '/frameworksManager', icon: 'Package', sortOrder: 4 },
        { id: 6, title: '测试', path: '', icon: 'CircleDashed', sortOrder: 8 },
        { id: 7, title: 'API 测试', path: '/test', icon: 'Bot', sortOrder: 0, parentId: 6 },
        { id: 8, title: 'Toast 测试', path: '/test/toast', icon: 'MessageSquare', sortOrder: 1, parentId: 6 },
        { id: 9, title: '系统设置', path: '/settings', icon: 'Settings', sortOrder: 9 }
      ];

      for (const menu of defaultMenus) {
        await prisma.menu.create({
          data: {
            title: menu.title,
            path: menu.path || null,
            icon: menu.icon || null,
            sortOrder: menu.sortOrder,
            parentId: menu.parentId || null
          }
        });
      }

      logger.info("默认菜单数据初始化完成");
    }
  } catch (error) {
    logger.error("初始化默认菜单失败:", error as Record<string, any>);
    throw new Error("初始化默认菜单失败");
  }
}
