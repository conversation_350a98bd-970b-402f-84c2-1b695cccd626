import { prisma } from '../db/prisma';
import { logger } from '../utils/logger';
import type { Application, ApplicationStatus } from '@prisma/client';

// 应用创建参数
export interface CreateApplicationParams {
  name: string;
  description?: string;
  userId: string;
  frameworkId?: string;
  config?: any;
}

// 应用更新参数
export interface UpdateApplicationParams {
  name?: string;
  description?: string;
  frameworkId?: string;
  status?: ApplicationStatus;
  config?: any;
}

// 应用列表查询参数
export interface ApplicationListParams {
  userId?: string;
  status?: ApplicationStatus;
  page?: number;
  pageSize?: number;
}

// 分页响应
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * 创建应用
 */
export async function createApplication(params: CreateApplicationParams): Promise<Application> {
  try {
    const application = await prisma.application.create({
      data: {
        name: params.name,
        description: params.description,
        userId: params.userId,
        frameworkId: params.frameworkId,
        config: params.config || {},
        status: 'CREATING'
      }
    });

    logger.info(`应用创建成功: ${application.id}`, { applicationId: application.id, userId: params.userId });
    
    // 异步创建容器（不等待完成）
    createContainerForApplication(application.id).catch(error => {
      logger.error(`容器创建失败: ${application.id}`, error);
    });

    return application;
  } catch (error) {
    logger.error("创建应用失败:", error as Record<string, any>);
    throw new Error("创建应用失败");
  }
}

/**
 * 获取用户的应用列表
 */
export async function getUserApplications(params: ApplicationListParams): Promise<PaginatedResponse<Application>> {
  try {
    const { userId, status, page = 1, pageSize = 10 } = params;

    const where: any = {};
    if (userId) where.userId = userId;
    if (status) where.status = status;

    const [applications, total] = await Promise.all([
      prisma.application.findMany({
        where,
        include: {
          framework: {
            select: {
              id: true,
              name: true,
              description: true,
              status: true
            }
          },
          container: true,
          _count: {
            select: {
              conversations: true,
              codeChanges: true
            }
          }
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.application.count({ where })
    ]);

    return {
      data: applications,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  } catch (error) {
    logger.error("获取应用列表失败:", error as Record<string, any>);
    throw new Error("获取应用列表失败");
  }
}

/**
 * 根据ID获取应用详情
 */
export async function getApplicationById(id: string): Promise<Application | null> {
  try {
    return await prisma.application.findUnique({
      where: { id },
      include: {
        user: {
          select: { id: true, username: true, name: true }
        },
        framework: {
          select: {
            id: true,
            name: true,
            description: true,
            status: true
          }
        },
        container: true,
        _count: {
          select: {
            conversations: true,
            codeChanges: true
          }
        }
      }
    });
  } catch (error) {
    logger.error("获取应用详情失败:", error as Record<string, any>);
    throw new Error("获取应用详情失败");
  }
}

/**
 * 更新应用
 */
export async function updateApplication(id: string, params: UpdateApplicationParams): Promise<Application> {
  try {
    const application = await prisma.application.update({
      where: { id },
      data: {
        ...params,
        updatedAt: new Date()
      }
    });

    logger.info(`应用更新成功: ${id}`, { applicationId: id });
    return application;
  } catch (error) {
    logger.error("更新应用失败:", error as Record<string, any>);
    throw new Error("更新应用失败");
  }
}

/**
 * 删除应用
 */
export async function deleteApplication(id: string): Promise<void> {
  try {
    // 先停止并删除容器
    const application = await prisma.application.findUnique({
      where: { id },
      include: { container: true }
    });

    if (application?.container) {
      await deleteContainerForApplication(application.container.id);
    }

    // 删除应用（级联删除相关数据）
    await prisma.application.delete({
      where: { id }
    });

    logger.info(`应用删除成功: ${id}`, { applicationId: id });
  } catch (error) {
    logger.error("删除应用失败:", error as Record<string, any>);
    throw new Error("删除应用失败");
  }
}

/**
 * 为应用创建容器（异步）
 */
async function createContainerForApplication(applicationId: string): Promise<void> {
  try {
    // 更新应用状态为创建中
    await updateApplication(applicationId, { status: 'CREATING' });

    // TODO: 调用Docker API创建容器
    // 这里需要集成实际的容器创建逻辑
    const containerConfig = {
      image: 'node:18-alpine', // 基础镜像
      ports: { '3000': '3000', '5432': '5432' }, // 端口映射
      environment: {
        NODE_ENV: 'development',
        DATABASE_URL: 'postgresql://user:pass@localhost:5432/appdb'
      }
    };

    // 创建容器记录
    const container = await prisma.container.create({
      data: {
        applicationId,
        status: 'CREATING',
        config: containerConfig,
        ports: containerConfig.ports
      }
    });

    // 模拟容器创建过程
    setTimeout(async () => {
      try {
        // 更新容器状态为运行中
        await prisma.container.update({
          where: { id: container.id },
          data: { 
            status: 'RUNNING',
            dockerId: `docker_${Date.now()}` // 模拟Docker ID
          }
        });

        // 更新应用状态为活跃
        await updateApplication(applicationId, { status: 'ACTIVE' });

        logger.info(`容器创建完成: ${container.id}`, { applicationId, containerId: container.id });
      } catch (error) {
        logger.error(`容器启动失败: ${container.id}`, error);
        await updateApplication(applicationId, { status: 'ERROR' });
      }
    }, 5000); // 模拟5秒创建时间

  } catch (error) {
    logger.error(`容器创建失败: ${applicationId}`, error);
    await updateApplication(applicationId, { status: 'ERROR' });
    throw error;
  }
}

/**
 * 删除应用的容器
 */
async function deleteContainerForApplication(containerId: string): Promise<void> {
  try {
    const container = await prisma.container.findUnique({
      where: { id: containerId }
    });

    if (container?.dockerId) {
      // TODO: 调用Docker API删除容器
      logger.info(`删除Docker容器: ${container.dockerId}`);
    }

    // 删除容器记录
    await prisma.container.delete({
      where: { id: containerId }
    });

    logger.info(`容器删除成功: ${containerId}`);
  } catch (error) {
    logger.error(`容器删除失败: ${containerId}`, error);
    throw error;
  }
}
