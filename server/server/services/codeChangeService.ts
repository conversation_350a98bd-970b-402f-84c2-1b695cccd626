import { prisma } from '../db/prisma';
import { logger } from '../utils/logger';
import type { CodeChange } from '@prisma/client';

// 代码变更创建参数
export interface CreateCodeChangeParams {
  applicationId: string;
  filePath: string;
  changeType: 'create' | 'update' | 'delete';
  oldContent?: string;
  newContent?: string;
  description?: string;
  conversationId?: string;
}

// 代码变更查询参数
export interface CodeChangeListParams {
  applicationId: string;
  filePath?: string;
  changeType?: string;
  conversationId?: string;
  page?: number;
  pageSize?: number;
}

/**
 * 记录代码变更
 */
export async function createCodeChange(params: CreateCodeChangeParams): Promise<CodeChange> {
  try {
    const codeChange = await prisma.codeChange.create({
      data: {
        applicationId: params.applicationId,
        filePath: params.filePath,
        changeType: params.changeType,
        oldContent: params.oldContent,
        newContent: params.newContent,
        description: params.description,
        conversationId: params.conversationId
      }
    });

    logger.info(`代码变更记录成功: ${codeChange.id}`, { 
      codeChangeId: codeChange.id,
      applicationId: params.applicationId,
      filePath: params.filePath,
      changeType: params.changeType
    });

    return codeChange;
  } catch (error) {
    logger.error("记录代码变更失败:", error as Record<string, any>);
    throw new Error("记录代码变更失败");
  }
}

/**
 * 获取应用的代码变更历史
 */
export async function getCodeChangeHistory(params: CodeChangeListParams) {
  try {
    const { applicationId, filePath, changeType, conversationId, page = 1, pageSize = 50 } = params;

    const where: any = { applicationId };
    if (filePath) where.filePath = { contains: filePath };
    if (changeType) where.changeType = changeType;
    if (conversationId) where.conversationId = conversationId;

    const [codeChanges, total] = await Promise.all([
      prisma.codeChange.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.codeChange.count({ where })
    ]);

    return {
      data: codeChanges,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  } catch (error) {
    logger.error("获取代码变更历史失败:", error as Record<string, any>);
    throw new Error("获取代码变更历史失败");
  }
}

/**
 * 获取文件的变更历史
 */
export async function getFileChangeHistory(applicationId: string, filePath: string): Promise<CodeChange[]> {
  try {
    return await prisma.codeChange.findMany({
      where: {
        applicationId,
        filePath
      },
      orderBy: { createdAt: 'desc' }
    });
  } catch (error) {
    logger.error("获取文件变更历史失败:", error as Record<string, any>);
    throw new Error("获取文件变更历史失败");
  }
}

/**
 * 获取最新的文件内容
 */
export async function getLatestFileContent(applicationId: string, filePath: string): Promise<string | null> {
  try {
    const latestChange = await prisma.codeChange.findFirst({
      where: {
        applicationId,
        filePath,
        changeType: { in: ['create', 'update'] }
      },
      orderBy: { createdAt: 'desc' }
    });

    return latestChange?.newContent || null;
  } catch (error) {
    logger.error("获取最新文件内容失败:", error as Record<string, any>);
    throw new Error("获取最新文件内容失败");
  }
}

/**
 * 获取应用的文件列表
 */
export async function getApplicationFiles(applicationId: string): Promise<string[]> {
  try {
    const files = await prisma.codeChange.findMany({
      where: {
        applicationId,
        changeType: { not: 'delete' }
      },
      select: { filePath: true },
      distinct: ['filePath'],
      orderBy: { filePath: 'asc' }
    });

    return files.map(f => f.filePath);
  } catch (error) {
    logger.error("获取应用文件列表失败:", error as Record<string, any>);
    throw new Error("获取应用文件列表失败");
  }
}

/**
 * 批量创建代码变更（用于AI生成多个文件）
 */
export async function createBatchCodeChanges(
  applicationId: string,
  changes: Omit<CreateCodeChangeParams, 'applicationId'>[],
  conversationId?: string
): Promise<CodeChange[]> {
  try {
    const codeChanges = await Promise.all(
      changes.map(change => 
        createCodeChange({
          ...change,
          applicationId,
          conversationId
        })
      )
    );

    logger.info(`批量代码变更记录成功: ${codeChanges.length}个文件`, { 
      applicationId,
      conversationId,
      fileCount: codeChanges.length
    });

    return codeChanges;
  } catch (error) {
    logger.error("批量记录代码变更失败:", error as Record<string, any>);
    throw new Error("批量记录代码变更失败");
  }
}

/**
 * 获取代码变更统计
 */
export async function getCodeChangeStats(applicationId: string) {
  try {
    const [total, byType, fileCount] = await Promise.all([
      prisma.codeChange.count({
        where: { applicationId }
      }),
      prisma.codeChange.groupBy({
        by: ['changeType'],
        where: { applicationId },
        _count: { changeType: true }
      }),
      prisma.codeChange.findMany({
        where: { applicationId },
        select: { filePath: true },
        distinct: ['filePath']
      })
    ]);

    return {
      total,
      fileCount: fileCount.length,
      byType: byType.reduce((acc, item) => {
        acc[item.changeType] = item._count.changeType;
        return acc;
      }, {} as Record<string, number>)
    };
  } catch (error) {
    logger.error("获取代码变更统计失败:", error as Record<string, any>);
    throw new Error("获取代码变更统计失败");
  }
}

/**
 * 模拟AI生成代码并记录变更
 */
export async function simulateAiCodeGeneration(
  applicationId: string,
  conversationId: string,
  userRequest: string
): Promise<CodeChange[]> {
  try {
    // 模拟AI根据用户请求生成的代码文件
    const generatedFiles = [
      {
        filePath: 'src/components/UserProfile.vue',
        changeType: 'create' as const,
        newContent: `<template>
  <div class="user-profile">
    <h2>用户资料</h2>
    <form @submit.prevent="updateProfile">
      <input v-model="user.name" placeholder="姓名" />
      <input v-model="user.email" placeholder="邮箱" />
      <button type="submit">更新</button>
    </form>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const user = ref({
  name: '',
  email: ''
})

const updateProfile = () => {
  console.log('更新用户资料:', user.value)
}
</script>`,
        description: `根据用户需求"${userRequest}"生成的用户资料组件`
      },
      {
        filePath: 'src/api/user.js',
        changeType: 'create' as const,
        newContent: `export const userApi = {
  async getProfile() {
    const response = await fetch('/api/user/profile')
    return response.json()
  },
  
  async updateProfile(data) {
    const response = await fetch('/api/user/profile', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    })
    return response.json()
  }
}`,
        description: `根据用户需求"${userRequest}"生成的用户API`
      }
    ];

    // 批量创建代码变更记录
    const codeChanges = await createBatchCodeChanges(
      applicationId,
      generatedFiles,
      conversationId
    );

    logger.info(`AI代码生成完成: ${codeChanges.length}个文件`, { 
      applicationId,
      conversationId,
      userRequest
    });

    return codeChanges;
  } catch (error) {
    logger.error("AI代码生成失败:", error as Record<string, any>);
    throw new Error("AI代码生成失败");
  }
}
