import { PrismaClient } from '@prisma/client'
import { hashPassword } from '../utils/crypto'
import { initDefaultMenus } from '../services/menuService'
import { initDefaultSystemConfigs } from '../utils/systemConfig'
import { initDefaultFrameworks } from '../services/initFrameworks'

// 全局变量声明，避免在开发环境中重复创建实例
declare global {
  var __prisma: PrismaClient | undefined
}

// 创建 Prisma 客户端实例
export const prisma = globalThis.__prisma || new PrismaClient({
  log: ['error'], // 只记录错误，减少内存使用
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  }
})

// 在开发环境中将实例保存到全局变量，避免热重载时重复创建
if (process.env.NODE_ENV !== 'production') {
  globalThis.__prisma = prisma
}

/**
 * 初始化数据库
 * 创建默认管理员用户和基础数据
 */
export async function initDB() {
  try {
    // 检查数据库连接
    await prisma.$connect()
    console.log('✅ 数据库连接成功')

    // 检查是否已有管理员用户
    const adminUser = await prisma.user.findUnique({
      where: { username: 'admin' }
    })

    if (!adminUser) {
      // 创建默认管理员用户
      const hashedPassword = await hashPassword('Admin@123')
      
      await prisma.user.create({
        data: {
          username: 'admin',
          email: '<EMAIL>',
          password: hashedPassword,
          name: '系统管理员',
          role: 'ADMIN'
        }
      })
      
      console.log('✅ 默认管理员用户已创建')
      console.log('   用户名: admin')
      console.log('   密码: Admin@123')
      console.log('   这是一个管理后台模板，主要功能：用户管理、文件管理(S3)、菜单管理')
    }

    // 初始化默认菜单
    await initDefaultMenus()

    // 初始化默认系统配置
    await initDefaultSystemConfigs()

    // 初始化默认框架
    await initDefaultFrameworks()

    console.log('✅ 数据库初始化完成')
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error)
    throw error
  }
}

/**
 * 优雅关闭数据库连接
 */
export async function closeDB() {
  await prisma.$disconnect()
  console.log('✅ 数据库连接已关闭')
}

// 导出 Prisma 客户端
export default prisma
