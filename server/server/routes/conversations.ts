import { Elysia, t } from "elysia";
import { requireAuth } from "../utils/auth";
import {
  processUserMessage,
  getConversationHistory,
  getRecentConversations,
  deleteConversation,
  getConversationStats
} from "../services/aiConversationService";
import { getApplicationById } from "../services/applicationService";

export const conversationsRoutes = new Elysia()
  .use(requireAuth)
  /**
   * 发送消息给AI
   */
  .post("/conversations", async ({ body, user }) => {
        try {
          // 检查应用权限
          const application = await getApplicationById(body.applicationId);
          if (!application || (application.userId !== user.id && user.role !== 'ADMIN')) {
            return new Response(JSON.stringify({
              success: false,
              error: "无权限访问此应用"
            }), {
              status: 403,
              headers: { "Content-Type": "application/json" }
            });
          }

          const conversation = await processUserMessage(
            body.applicationId,
            user.id,
            body.message,
            body.messageType || 'chat'
          );

          return {
            success: true,
            data: conversation,
            message: "消息发送成功，AI正在处理..."
          };
        } catch (error: any) {
          return new Response(JSON.stringify({
            success: false,
            error: error.message
          }), {
            status: 400,
            headers: { "Content-Type": "application/json" }
          });
        }
      }, {
        body: t.Object({
          applicationId: t.String(),
          message: t.String({ minLength: 1 }),
          messageType: t.Optional(t.Union([
            t.Literal('chat'),
            t.Literal('code_request'),
            t.Literal('code_review'),
            t.Literal('debug'),
            t.Literal('explain')
          ]))
        })
      })

  /**
   * 获取应用的对话历史
   */
  .get("/conversations/application/:applicationId", async ({ params, query, user }) => {
        try {
          // 检查应用权限
          const application = await getApplicationById(params.applicationId);
          if (!application || (application.userId !== user.id && user.role !== 'ADMIN')) {
            return new Response(JSON.stringify({
              success: false,
              error: "无权限访问此应用"
            }), {
              status: 403,
              headers: { "Content-Type": "application/json" }
            });
          }

          const queryParams = {
            applicationId: params.applicationId,
            messageType: query.messageType as string,
            page: query.page ? parseInt(query.page as string) : 1,
            pageSize: query.pageSize ? parseInt(query.pageSize as string) : 50
          };

          const result = await getConversationHistory(queryParams);

          return {
            success: true,
            ...result
          };
        } catch (error: any) {
          return new Response(JSON.stringify({
            success: false,
            error: error.message
          }), {
            status: 500,
            headers: { "Content-Type": "application/json" }
          });
        }
      })

  /**
   * 获取最近的对话
   */
  .get("/conversations/application/:applicationId/recent", async ({ params, query, user }) => {
        try {
          // 检查应用权限
          const application = await getApplicationById(params.applicationId);
          if (!application || (application.userId !== user.id && user.role !== 'ADMIN')) {
            return new Response(JSON.stringify({
              success: false,
              error: "无权限访问此应用"
            }), {
              status: 403,
              headers: { "Content-Type": "application/json" }
            });
          }

          const limit = query.limit ? parseInt(query.limit as string) : 10;
          const conversations = await getRecentConversations(params.applicationId, limit);

          return {
            success: true,
            data: conversations
          };
        } catch (error: any) {
          return new Response(JSON.stringify({
            success: false,
            error: error.message
          }), {
            status: 500,
            headers: { "Content-Type": "application/json" }
          });
        }
      })

  /**
   * 获取对话统计
   */
  .get("/conversations/application/:applicationId/stats", async ({ params, user }) => {
        try {
          // 检查应用权限
          const application = await getApplicationById(params.applicationId);
          if (!application || (application.userId !== user.id && user.role !== 'ADMIN')) {
            return new Response(JSON.stringify({
              success: false,
              error: "无权限访问此应用"
            }), {
              status: 403,
              headers: { "Content-Type": "application/json" }
            });
          }

          const stats = await getConversationStats(params.applicationId);

          return {
            success: true,
            data: stats
          };
        } catch (error: any) {
          return new Response(JSON.stringify({
            success: false,
            error: error.message
          }), {
            status: 500,
            headers: { "Content-Type": "application/json" }
          });
        }
      })

  /**
   * 删除对话
   */
  .delete("/conversations/:id", async ({ params, user }) => {
        try {
          // TODO: 添加权限检查，确保用户只能删除自己的对话
          await deleteConversation(params.id);

          return {
            success: true,
            message: "对话删除成功"
          };
        } catch (error: any) {
          return new Response(JSON.stringify({
            success: false,
            error: error.message
          }), {
            status: 400,
            headers: { "Content-Type": "application/json" }
          });
        }
      })

  /**
   * 快速发送代码请求
   */
  .post("/conversations/code-request", async ({ body, user }) => {
        try {
          // 检查应用权限
          const application = await getApplicationById(body.applicationId);
          if (!application || (application.userId !== user.id && user.role !== 'ADMIN')) {
            return new Response(JSON.stringify({
              success: false,
              error: "无权限访问此应用"
            }), {
              status: 403,
              headers: { "Content-Type": "application/json" }
            });
          }

          const conversation = await processUserMessage(
            body.applicationId,
            user.id,
            body.requirement,
            'code_request'
          );

          return {
            success: true,
            data: conversation,
            message: "代码请求已提交，AI正在生成代码..."
          };
        } catch (error: any) {
          return new Response(JSON.stringify({
            success: false,
            error: error.message
          }), {
            status: 400,
            headers: { "Content-Type": "application/json" }
          });
        }
      }, {
        body: t.Object({
          applicationId: t.String(),
          requirement: t.String({ minLength: 1 }),
          context: t.Optional(t.String()) // 额外的上下文信息
        })
      })

  /**
   * 代码审查请求
   */
  .post("/conversations/code-review", async ({ body, user }) => {
        try {
          // 检查应用权限
          const application = await getApplicationById(body.applicationId);
          if (!application || (application.userId !== user.id && user.role !== 'ADMIN')) {
            return new Response(JSON.stringify({
              success: false,
              error: "无权限访问此应用"
            }), {
              status: 403,
              headers: { "Content-Type": "application/json" }
            });
          }

          const message = `请审查以下代码：\n\n文件：${body.filePath}\n\n代码：\n${body.code}`;
          
          const conversation = await processUserMessage(
            body.applicationId,
            user.id,
            message,
            'code_review'
          );

          return {
            success: true,
            data: conversation,
            message: "代码审查请求已提交，AI正在分析..."
          };
        } catch (error: any) {
          return new Response(JSON.stringify({
            success: false,
            error: error.message
          }), {
            status: 400,
            headers: { "Content-Type": "application/json" }
          });
        }
      }, {
        body: t.Object({
          applicationId: t.String(),
          filePath: t.String(),
          code: t.String({ minLength: 1 })
        })
      })
  
