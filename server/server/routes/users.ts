import { Elysia, t } from "elysia";
import {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  validateUser,
  getFilteredUsers,
  getUsersPaginated
} from "../services/userService";
import type {
  UserFilterParams,
  UserListParams,
  PaginatedResponse,
  CreateUserParams,
  UpdateUserParams
} from "../services/userService";
import { logger } from "../utils/logger";
import { generateToken, refreshToken } from "../utils/jwt";
import { requireAuth, requireAdmin, requireSelfOrAdmin } from "../utils/auth";
import { handleGoogleLogin } from "../services/googleAuthService";

/**
 * 用户路由模块
 */
export const users = new Elysia()
  // 公开接口 - 不需要身份验证
  /**
   * 用户登录
   */
  .post("/users/login", async ({ body }) => {
    const { username, password } = body;

    // 尝试验证
    const user = await validateUser(username, password);
    logger.debug('用户验证结果', { success: !!user });

    if (!user) {
      return new Response(JSON.stringify({ error: "用户名或密码错误" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    // 生成JWT令牌
    const token = await generateToken(user);
    logger.debug('生成令牌成功');

    // 返回用户信息时排除密码字段
    const { password: _, ...userWithoutPassword } = user;

    return {
      user: userWithoutPassword,
      token
    };
  }, {
    body: t.Object({
      username: t.String(),
      password: t.String(),
    }),
    detail: {
      tags: ["用户接口"],
      summary: "用户登录",
      description: "用户登录验证",
      responses: {
        200: {
          description: "登录成功",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  user: {
                    type: "object",
                    properties: {
                      id: { type: "number" },
                      username: { type: "string" },
                      name: { type: "string" },
                      email: { type: "string" },
                      role: { type: "string" },
                    },
                  },
                  token: { type: "string" },
                },
              },
            },
          },
        },
        401: {
          description: "登录失败",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: { type: "string" },
                },
              },
            },
          },
        },
      },
    },
  })

  /**
   * Google登录
   */
  .post("/users/google-login", async ({ body }) => {
    const { credential } = body;

    try {
      const result = await handleGoogleLogin(credential);
      logger.debug('Google登录成功');

      return result;
    } catch (error: any) {
      logger.error('Google登录失败', { error: error.message });
      return new Response(JSON.stringify({ error: error.message }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    body: t.Object({
      credential: t.String(),
    }),
    detail: {
      tags: ["用户接口"],
      summary: "Google登录",
      description: "使用Google OAuth进行用户登录",
      responses: {
        200: {
          description: "登录成功",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  user: {
                    type: "object",
                    description: "用户信息"
                  },
                  token: {
                    type: "string",
                    description: "JWT令牌"
                  }
                }
              }
            }
          }
        },
        400: {
          description: "登录失败",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: {
                    type: "string",
                    description: "错误信息"
                  }
                }
              }
            }
          }
        }
      }
    }
  })

  /**
   * 用户注册
   */
  .post("/users/register", async ({ body }) => {
    const { username, email, password } = body;

    try {
      // 检查用户名是否已存在
      const existingUser = await validateUser(username, '');
      if (existingUser) {
        return new Response(JSON.stringify({ error: "用户名已存在" }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        });
      }

      // 创建新用户
      const newUser = await createUser({
        username,
        email,
        password,
        name: username, // 使用用户名作为默认姓名
        role: 'USER' // 默认角色
      });

      logger.debug('用户注册成功', { username });

      return {
        message: "注册成功",
        user: {
          id: newUser.id,
          username: newUser.username,
          email: newUser.email,
          name: newUser.name,
          role: newUser.role
        }
      };
    } catch (error: any) {
      logger.error('用户注册失败', { error: error.message });
      return new Response(JSON.stringify({ error: "注册失败: " + error.message }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    body: t.Object({
      username: t.String(),
      email: t.String(),
      password: t.String(),
    }),
    detail: {
      tags: ["用户接口"],
      summary: "用户注册",
      description: "创建新用户账号",
      responses: {
        200: {
          description: "注册成功",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  message: {
                    type: "string",
                    example: "注册成功",
                  },
                  user: {
                    type: "object",
                    properties: {
                      id: { type: "string" },
                      username: { type: "string" },
                      email: { type: "string" },
                      name: { type: "string" },
                      role: { type: "string" }
                    }
                  }
                },
              },
            },
          },
        },
        400: {
          description: "用户名已存在",
        },
        500: {
          description: "注册失败",
        },
      },
    },
  })
  /**
   * 刷新令牌
   */
  .post("/users/refresh-token", async ({ headers }) => {
    const authHeader = headers.authorization;
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return new Response(JSON.stringify({ error: "未提供令牌" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    try {
      const newToken = await refreshToken(token);
      logger.debug('令牌刷新成功');
      return { token: newToken };
    } catch (error) {
      return new Response(JSON.stringify({ error: "刷新令牌失败" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    detail: {
      tags: ["用户接口"],
      summary: "刷新令牌",
      description: "刷新用户访问令牌",
      responses: {
        200: {
          description: "刷新成功",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  token: { type: "string" },
                },
              },
            },
          },
        },
        401: {
          description: "刷新失败",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: { type: "string" },
                },
              },
            },
          },
        },
      },
    },
  })

  // 需要管理员权限的接口
  .group("/users", (app) =>
    app
      .use(requireAdmin)
      /**
       * 分页获取用户列表 - 需要管理员权限
       */
      .get("/list", ({ query }) => {
        const params: UserListParams = {
          page: query.page ? parseInt(query.page as string) : 1,
          pageSize: query.pageSize ? parseInt(query.pageSize as string) : 10,
          search: query.search as string,
          role: query.role as any // 临时处理类型转换
        };

        return getUsersPaginated(params);
      }, {
        query: t.Object({
          page: t.Optional(t.String()),
          pageSize: t.Optional(t.String()),
          search: t.Optional(t.String()),
          role: t.Optional(t.String()),
        }),
        detail: {
          tags: ["用户接口"],
          summary: "分页获取用户列表",
          description: "分页返回用户列表，支持搜索和角色筛选（需要管理员权限）",
          security: [{ bearerAuth: [] }],
          responses: {
            200: {
              description: "成功响应",
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      data: {
                        type: "array",
                        items: {
                          type: "object",
                          properties: {
                            id: { type: "number" },
                            username: { type: "string" },
                            name: { type: "string" },
                            email: { type: "string" },
                            role: { type: "string" },
                            created_at: { type: "string" },
                            updated_at: { type: "string" },
                          },
                        },
                      },
                      total: { type: "number" },
                      page: { type: "number" },
                      pageSize: { type: "number" },
                      totalPages: { type: "number" },
                    },
                  },
                },
              },
            },
            401: {
              description: "未授权",
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      error: { type: "string" },
                    },
                  },
                },
              },
            },
            403: {
              description: "权限不足",
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      error: { type: "string" },
                    },
                  },
                },
              },
            },
          },
        },
      })
      /**
       * 筛选用户 - 需要管理员权限
       */
      .get("/filter", ({ query }) => {
        const filters: UserFilterParams = {
          search: query.search as string,
          role: query.role as any // 临时处理类型转换
        };

        return getFilteredUsers(filters);
      }, {
    query: t.Object({
      search: t.Optional(t.String()),
      role: t.Optional(t.String()),
    }),
    detail: {
      tags: ["用户接口"],
      summary: "筛选用户",
      description: "根据条件筛选用户",
      responses: {
        200: {
          description: "成功响应",
          content: {
            "application/json": {
              schema: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    id: { type: "number" },
                    username: { type: "string" },
                    name: { type: "string" },
                    email: { type: "string" },
                    role: { type: "string" },
                    created_at: { type: "string" },
                    updated_at: { type: "string" },
                  },
                },
              },
            },
          },
        },
      },
    },
  })
      /**
       * 创建用户 - 需要管理员权限
       */
      .post("/", async ({ body }) => {
        try {
          // 设置默认角色
          const userParams: CreateUserParams = {
            username: body.username,
            password: body.password,
            name: body.name,
            email: body.email,
            role: (body.role || 'USER') as any
          };

          const user = await createUser(userParams);
          return { id: user.id, success: true };
        } catch (error: any) {
          return new Response(JSON.stringify({ error: error.message }), {
            status: 400,
            headers: { "Content-Type": "application/json" },
          });
        }
      }, {
        body: t.Object({
          username: t.String(),
          password: t.String(),
          name: t.String(),
          email: t.Optional(t.String()),
          role: t.Optional(t.String()),
        }),
        detail: {
          tags: ["用户接口"],
          summary: "创建用户",
          description: "创建新的用户（需要管理员权限）",
          security: [{ bearerAuth: [] }],
          responses: {
            200: {
              description: "创建成功",
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      id: { type: "number" },
                      success: { type: "boolean" },
                    },
                  },
                },
              },
            },
            400: {
              description: "创建失败",
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      error: { type: "string" },
                    },
                  },
                },
              },
            },
          },
        },
      })
      /**
       * 删除用户 - 需要管理员权限
       */
      .delete("/:id", async ({ params }) => {
        try {
          const id = params.id;
          await deleteUser(id);
          return { success: true };
        } catch (error: any) {
          return new Response(JSON.stringify({ error: error.message }), {
            status: 400,
            headers: { "Content-Type": "application/json" },
          });
        }
      }, {
        params: t.Object({
          id: t.String(),
        }),
        detail: {
          tags: ["用户接口"],
          summary: "删除用户",
          description: "删除指定ID的用户（需要管理员权限）",
          security: [{ bearerAuth: [] }],
          responses: {
            200: {
              description: "删除成功",
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      success: { type: "boolean" },
                    },
                  },
                },
              },
            },
            400: {
              description: "删除失败",
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      error: { type: "string" },
                    },
                  },
                },
              },
            },
          },
        },
      })
  )

  // 需要基本认证的接口
  .group("/users", (app) =>
    app
      .use(requireAuth)
      /**
       * 获取当前用户信息
       */
      .get("/profile", async ({ user }) => {
        try {
          const userInfo = await getUserById(user.id);

          if (!userInfo) {
            return new Response(JSON.stringify({ error: "用户不存在" }), {
              status: 404,
              headers: { "Content-Type": "application/json" },
            });
          }

          return {
            success: true,
            data: userInfo
          };
        } catch (error: any) {
          logger.error('获取用户信息失败', { error: error.message });
          return new Response(JSON.stringify({ error: "获取用户信息失败" }), {
            status: 500,
            headers: { "Content-Type": "application/json" },
          });
        }
      }, {
        detail: {
          tags: ["用户接口"],
          summary: "获取当前用户信息",
          description: "返回当前登录用户的详细信息",
          security: [{ bearerAuth: [] }],
          responses: {
            200: {
              description: "成功响应",
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      success: { type: "boolean" },
                      data: {
                        type: "object",
                        properties: {
                          id: { type: "string" },
                          username: { type: "string" },
                          name: { type: "string" },
                          email: { type: "string" },
                          role: { type: "string" },
                          createdAt: { type: "string" },
                          updatedAt: { type: "string" },
                        }
                      }
                    },
                  },
                },
              },
            },
            401: {
              description: "未授权",
            },
            404: {
              description: "用户不存在",
            },
          },
        },
      })
  )

  // 需要认证的接口（用户可以访问自己的数据）
  .group("/users", (app) =>
    app
      .use(requireSelfOrAdmin)
      /**
       * 根据ID获取用户 - 需要认证
       */
      .get("/:id", async ({ params }) => {
        const id = params.id;
        const userInfo = await getUserById(id);

        if (!userInfo) {
          return new Response(JSON.stringify({ error: "用户不存在" }), {
            status: 404,
            headers: { "Content-Type": "application/json" },
          });
        }

        return userInfo;
      }, {
    params: t.Object({
      id: t.String(),
    }),
    detail: {
      tags: ["用户接口"],
      summary: "根据ID获取用户",
      description: "返回指定ID的用户信息",
      responses: {
        200: {
          description: "成功响应",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  id: { type: "number" },
                  username: { type: "string" },
                  name: { type: "string" },
                  email: { type: "string" },
                  role: { type: "string" },
                  created_at: { type: "string" },
                  updated_at: { type: "string" },
                },
              },
            },
          },
        },
        404: {
          description: "用户不存在",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: { type: "string" },
                },
              },
            },
          },
        },
      },
    },
  })
      /**
       * 更新用户 - 需要认证（用户可以更新自己的信息，管理员可以更新任何用户）
       */
      .put("/:id", async ({ params, body }) => {
        try {
          const id = params.id;

          // requireSelfOrAdmin 中间件已经处理了权限检查
          // 这里直接更新用户信息
          const updateParams: UpdateUserParams = {
            ...body,
            role: body.role as any // 临时处理类型转换
          };
          const user = await updateUser(id, updateParams);

          return { success: true, user };
        } catch (error: any) {
          return new Response(JSON.stringify({ error: error.message }), {
            status: 400,
            headers: { "Content-Type": "application/json" },
          });
        }
      }, {
    body: t.Object({
      username: t.Optional(t.String()),
      password: t.Optional(t.String()),
      name: t.Optional(t.String()),
      email: t.Optional(t.String()),
      role: t.Optional(t.String()),
    }),
    detail: {
      tags: ["用户接口"],
      summary: "创建用户",
      description: "创建新的用户",
      responses: {
        200: {
          description: "创建成功",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  id: { type: "number" },
                  success: { type: "boolean" },
                },
              },
            },
          },
        },
        400: {
          description: "创建失败",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: { type: "string" },
                },
              },
            },
          },
        },
      },
    },
  })
  );

export default users;