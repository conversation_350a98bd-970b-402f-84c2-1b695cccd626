import { Elysia, t } from "elysia";
import { requireAuth } from "../utils/auth";
import {
  createApplication,
  getUserApplications,
  getApplicationById,
  updateApplication,
  deleteApplication
} from "../services/applicationService";
import type { CreateApplicationParams, UpdateApplicationParams } from "../services/applicationService";

export const applicationsRoutes = new Elysia()
  .use(requireAuth)
  /**
   * 创建新应用
   */
  .post("/applications", async ({ body, user }) => {
        try {
          const params: CreateApplicationParams = {
            name: body.name,
            description: body.description,
            userId: user.id,
            frameworkId: body.frameworkId,
            config: body.config
          };

          const application = await createApplication(params);
          
          return {
            success: true,
            data: application,
            message: "应用创建成功，正在初始化开发环境..."
          };
        } catch (error: any) {
          return new Response(JSON.stringify({
            success: false,
            error: error.message
          }), {
            status: 400,
            headers: { "Content-Type": "application/json" }
          });
        }
      }, {
        body: t.Object({
          name: t.String({ minLength: 1, maxLength: 100 }),
          description: t.Optional(t.String({ maxLength: 500 })),
          frameworkId: t.Optional(t.String()),
          config: t.Optional(t.Any())
        })
      })

  /**
   * 获取用户的应用列表
   */
  .get("/applications", async ({ query, user }) => {
        try {
          const params = {
            userId: user.id,
            status: query.status as any,
            page: query.page ? parseInt(query.page as string) : 1,
            pageSize: query.pageSize ? parseInt(query.pageSize as string) : 10
          };

          const result = await getUserApplications(params);
          
          return {
            success: true,
            ...result
          };
        } catch (error: any) {
          return new Response(JSON.stringify({
            success: false,
            error: error.message
          }), {
            status: 500,
            headers: { "Content-Type": "application/json" }
          });
        }
      })

  /**
   * 获取应用详情
   */
  .get("/applications/:id", async ({ params, user }) => {
        try {
          const application = await getApplicationById(params.id);
          
          if (!application) {
            return new Response(JSON.stringify({
              success: false,
              error: "应用不存在"
            }), {
              status: 404,
              headers: { "Content-Type": "application/json" }
            });
          }

          // 检查权限：只有应用所有者可以查看
          if (application.userId !== user.id && user.role !== 'ADMIN') {
            return new Response(JSON.stringify({
              success: false,
              error: "无权限访问此应用"
            }), {
              status: 403,
              headers: { "Content-Type": "application/json" }
            });
          }

          return {
            success: true,
            data: application
          };
        } catch (error: any) {
          return new Response(JSON.stringify({
            success: false,
            error: error.message
          }), {
            status: 500,
            headers: { "Content-Type": "application/json" }
          });
        }
      })

  /**
   * 更新应用
   */
  .put("/applications/:id", async ({ params, body, user }) => {
        try {
          // 先检查应用是否存在和权限
          const existingApp = await getApplicationById(params.id);
          
          if (!existingApp) {
            return new Response(JSON.stringify({
              success: false,
              error: "应用不存在"
            }), {
              status: 404,
              headers: { "Content-Type": "application/json" }
            });
          }

          if (existingApp.userId !== user.id && user.role !== 'ADMIN') {
            return new Response(JSON.stringify({
              success: false,
              error: "无权限修改此应用"
            }), {
              status: 403,
              headers: { "Content-Type": "application/json" }
            });
          }

          const updateParams: UpdateApplicationParams = {
            name: body.name,
            description: body.description,
            frameworkId: body.frameworkId,
            status: body.status,
            config: body.config
          };

          const application = await updateApplication(params.id, updateParams);
          
          return {
            success: true,
            data: application,
            message: "应用更新成功"
          };
        } catch (error: any) {
          return new Response(JSON.stringify({
            success: false,
            error: error.message
          }), {
            status: 400,
            headers: { "Content-Type": "application/json" }
          });
        }
      }, {
        body: t.Object({
          name: t.Optional(t.String({ minLength: 1, maxLength: 100 })),
          description: t.Optional(t.String({ maxLength: 500 })),
          frameworkId: t.Optional(t.String()),
          status: t.Optional(t.Union([
            t.Literal('CREATING'),
            t.Literal('ACTIVE'),
            t.Literal('STOPPED'),
            t.Literal('ERROR'),
            t.Literal('DELETED')
          ])),
          config: t.Optional(t.Any())
        })
      })

  /**
   * 删除应用
   */
  .delete("/applications/:id", async ({ params, user }) => {
        try {
          // 先检查应用是否存在和权限
          const existingApp = await getApplicationById(params.id);
          
          if (!existingApp) {
            return new Response(JSON.stringify({
              success: false,
              error: "应用不存在"
            }), {
              status: 404,
              headers: { "Content-Type": "application/json" }
            });
          }

          if (existingApp.userId !== user.id && user.role !== 'ADMIN') {
            return new Response(JSON.stringify({
              success: false,
              error: "无权限删除此应用"
            }), {
              status: 403,
              headers: { "Content-Type": "application/json" }
            });
          }

          await deleteApplication(params.id);
          
          return {
            success: true,
            message: "应用删除成功"
          };
        } catch (error: any) {
          return new Response(JSON.stringify({
            success: false,
            error: error.message
          }), {
            status: 400,
            headers: { "Content-Type": "application/json" }
          });
        }
      })

  /**
   * 启动/停止应用容器
   */
  .post("/applications/:id/container/:action", async ({ params, user }) => {
        try {
          const { id, action } = params;
          
          // 检查权限
          const application = await getApplicationById(id);
          if (!application || (application.userId !== user.id && user.role !== 'ADMIN')) {
            return new Response(JSON.stringify({
              success: false,
              error: "无权限操作此应用"
            }), {
              status: 403,
              headers: { "Content-Type": "application/json" }
            });
          }

          let newStatus: 'CREATING' | 'ACTIVE' | 'STOPPED' | 'ERROR' | 'DELETED';
          let message: string;

          switch (action) {
            case 'start':
              newStatus = 'ACTIVE';
              message = '应用启动成功';
              // TODO: 实际启动容器逻辑
              break;
            case 'stop':
              newStatus = 'STOPPED';
              message = '应用停止成功';
              // TODO: 实际停止容器逻辑
              break;
            case 'restart':
              newStatus = 'ACTIVE';
              message = '应用重启成功';
              // TODO: 实际重启容器逻辑
              break;
            default:
              return new Response(JSON.stringify({
                success: false,
                error: "无效的操作"
              }), {
                status: 400,
                headers: { "Content-Type": "application/json" }
              });
          }

          const updatedApp = await updateApplication(id, { status: newStatus });
          
          return {
            success: true,
            data: updatedApp,
            message
          };
        } catch (error: any) {
          return new Response(JSON.stringify({
            success: false,
            error: error.message
          }), {
            status: 500,
            headers: { "Content-Type": "application/json" }
          });
        }
      })
