import { Elysia, t } from "elysia";
import {
  getFrameworks,
  getFrameworkById,
  createFramework,
  updateFramework,
  deleteFramework,
  getFrameworkStats,
  updateFrameworksOrder,
  getPublicFrameworks
} from "../services/frameworkService";
import { requireAuth, requireAdmin } from "../utils/auth";
import { logger } from "../utils/logger";

/**
 * 框架管理路由模块 (仅管理员)
 */
export const frameworksRoutes = new Elysia()

  /**
   * 公开的框架列表接口 (供前端web调用)
   */
  .get("/frameworks", async ({ query }) => {
    try {
      const { status } = query;

      // 使用专门的公开接口函数
      const frameworks = await getPublicFrameworks(status as any);

      return {
        success: true,
        data: frameworks
      };
    } catch (error: any) {
      logger.error('获取公开框架列表失败', { error: error.message });
      return new Response(JSON.stringify({
        success: false,
        error: error.message
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    query: t.Object({
      status: t.Optional(t.Union([
        t.Literal('ENABLED'),
        t.Literal('COMING_SOON'),
        t.Literal('DISABLED')
      ]))
    }),
    detail: {
      tags: ["公开接口"],
      summary: "获取框架列表",
      description: "获取可用的开发框架列表，供前端选择使用"
    }
  })

  // 测试路由 - 不需要权限
  .get("/frameworks/test", () => {
    return { success: true, message: "框架API正常工作" }
  })

  // 管理员路由
  .group("/admin", (app) => app
    .use(requireAdmin) // 管理员权限
  
  /**
   * 获取框架列表
   */
  .get("/frameworks", async ({ query }) => {
    try {
      const { page, pageSize, status, search } = query;
      const result = await getFrameworks({
        page: page ? parseInt(page) : undefined,
        pageSize: pageSize ? parseInt(pageSize) : undefined,
        status: status as any,
        search
      });
      
      return {
        success: true,
        ...result
      };
    } catch (error: any) {
      logger.error('获取框架列表失败', { error: error.message });
      return new Response(JSON.stringify({ 
        success: false, 
        error: error.message 
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    query: t.Object({
      page: t.Optional(t.String()),
      pageSize: t.Optional(t.String()),
      status: t.Optional(t.Union([
        t.Literal('ENABLED'), 
        t.Literal('COMING_SOON'), 
        t.Literal('DISABLED')
      ])),
      search: t.Optional(t.String())
    }),
    detail: {
      tags: ["框架管理"],
      summary: "获取框架列表",
      description: "分页获取框架列表，支持状态筛选和搜索"
    }
  })

  /**
   * 获取框架统计信息
   */
  .get("/frameworks/stats", async () => {
    try {
      const stats = await getFrameworkStats();
      
      return {
        success: true,
        data: stats
      };
    } catch (error: any) {
      logger.error('获取框架统计失败', { error: error.message });
      return new Response(JSON.stringify({ 
        success: false, 
        error: error.message 
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    detail: {
      tags: ["框架管理"],
      summary: "获取框架统计",
      description: "获取框架数量统计信息"
    }
  })

  /**
   * 根据ID获取框架详情
   */
  .get("/frameworks/:id", async ({ params }) => {
    try {
      const framework = await getFrameworkById(params.id);
      
      return {
        success: true,
        data: framework
      };
    } catch (error: any) {
      logger.error('获取框架详情失败', { error: error.message, frameworkId: params.id });
      return new Response(JSON.stringify({ 
        success: false, 
        error: error.message 
      }), {
        status: error.message === '框架不存在' ? 404 : 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    params: t.Object({
      id: t.String()
    }),
    detail: {
      tags: ["框架管理"],
      summary: "获取框架详情",
      description: "根据框架ID获取详细信息"
    }
  })

  /**
   * 创建新框架
   */
  .post("/frameworks", async ({ body }) => {
    try {
      const framework = await createFramework(body as any);
      
      return {
        success: true,
        data: framework,
        message: "框架创建成功"
      };
    } catch (error: any) {
      logger.error('创建框架失败', { error: error.message, data: body });
      return new Response(JSON.stringify({ 
        success: false, 
        error: error.message 
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    body: t.Object({
      name: t.String({ minLength: 1, maxLength: 100 }),
      description: t.Optional(t.String({ maxLength: 500 })),
      status: t.Optional(t.Union([
        t.Literal('ENABLED'), 
        t.Literal('COMING_SOON'), 
        t.Literal('DISABLED')
      ])),
      introduction: t.Optional(t.String({ maxLength: 2000 })),
      sortOrder: t.Optional(t.Number({ minimum: 0 }))
    }),
    detail: {
      tags: ["框架管理"],
      summary: "创建框架",
      description: "创建新的开发框架"
    }
  })

  /**
   * 更新框架
   */
  .put("/frameworks/:id", async ({ params, body }) => {
    try {
      const framework = await updateFramework(params.id, body as any);
      
      return {
        success: true,
        data: framework,
        message: "框架更新成功"
      };
    } catch (error: any) {
      logger.error('更新框架失败', { error: error.message, frameworkId: params.id });
      return new Response(JSON.stringify({ 
        success: false, 
        error: error.message 
      }), {
        status: error.message === '框架不存在' ? 404 : 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    params: t.Object({
      id: t.String()
    }),
    body: t.Object({
      name: t.Optional(t.String({ minLength: 1, maxLength: 100 })),
      description: t.Optional(t.String({ maxLength: 500 })),
      status: t.Optional(t.Union([
        t.Literal('ENABLED'), 
        t.Literal('COMING_SOON'), 
        t.Literal('DISABLED')
      ])),
      introduction: t.Optional(t.String({ maxLength: 2000 })),
      sortOrder: t.Optional(t.Number({ minimum: 0 }))
    }),
    detail: {
      tags: ["框架管理"],
      summary: "更新框架",
      description: "更新框架信息"
    }
  })

  /**
   * 删除框架
   */
  .delete("/frameworks/:id", async ({ params }) => {
    try {
      await deleteFramework(params.id);
      
      return {
        success: true,
        message: "框架删除成功"
      };
    } catch (error: any) {
      logger.error('删除框架失败', { error: error.message, frameworkId: params.id });
      return new Response(JSON.stringify({ 
        success: false, 
        error: error.message 
      }), {
        status: error.message === '框架不存在' ? 404 : 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    params: t.Object({
      id: t.String()
    }),
    detail: {
      tags: ["框架管理"],
      summary: "删除框架",
      description: "删除指定框架"
    }
  })

  /**
   * 批量更新框架排序
   */
  .put("/frameworks/order", async ({ body }) => {
    try {
      await updateFrameworksOrder(body.orders);
      
      return {
        success: true,
        message: "框架排序更新成功"
      };
    } catch (error: any) {
      logger.error('更新框架排序失败', { error: error.message });
      return new Response(JSON.stringify({ 
        success: false, 
        error: error.message 
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
  }, {
    body: t.Object({
      orders: t.Array(t.Object({
        id: t.String(),
        sortOrder: t.Number({ minimum: 0 })
      }))
    }),
    detail: {
      tags: ["框架管理"],
      summary: "更新框架排序",
      description: "批量更新框架的排序权重"
    }
  })
  ); // 关闭 group
