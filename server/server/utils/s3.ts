import { S3Client } from "@aws-sdk/client-s3"
import { prisma } from "../db/prisma"

// 导入Prisma类型
import type { S3Config } from '@prisma/client'

/**
 * 获取S3配置（所有配置，不过滤enabled状态）
 */
export async function getS3Config(): Promise<S3Config | null> {
  return await prisma.s3Config.findFirst({
    orderBy: { id: 'desc' }
  })
}

/**
 * 获取启用的S3配置（只返回enabled=true的配置）
 */
export async function getEnabledS3Config(): Promise<S3Config | null> {
  return await prisma.s3Config.findFirst({
    where: { enabled: true },
    orderBy: { id: 'desc' }
  })
}

/**
 * 保存S3配置
 */
export async function saveS3Config(config: Omit<S3Config, 'id' | 'createdAt' | 'updatedAt'>): Promise<S3Config> {
  // 检查是否已有配置
  const existingConfig = await getS3Config()

  if (existingConfig) {
    // 更新现有配置
    return await prisma.s3Config.update({
      where: { id: existingConfig.id },
      data: {
        enabled: config.enabled,
        endpointUrl: config.endpointUrl,
        awsAccessKeyId: config.awsAccessKeyId,
        awsSecretAccessKey: config.awsSecretAccessKey,
        regionName: config.regionName,
        bucketName: config.bucketName,
        folder: config.folder,
        bucketUrl: config.bucketUrl,
        updatedAt: new Date()
      }
    })
  } else {
    // 创建新配置
    return await prisma.s3Config.create({
      data: {
        enabled: config.enabled,
        endpointUrl: config.endpointUrl,
        awsAccessKeyId: config.awsAccessKeyId,
        awsSecretAccessKey: config.awsSecretAccessKey,
        regionName: config.regionName,
        bucketName: config.bucketName,
        folder: config.folder,
        bucketUrl: config.bucketUrl
      }
    })
  }
}

/**
 * 创建S3客户端（用于文件操作，需要启用的配置）
 */
export async function createS3Client() {
  const config = await getEnabledS3Config()
  if (!config) {
    throw new Error("S3配置未找到或未启用")
  }

  const s3ClientConfig: any = {
    region: config.regionName,
    credentials: {
      accessKeyId: config.awsAccessKeyId,
      secretAccessKey: config.awsSecretAccessKey,
    },
  }

  if (config.endpointUrl) {
    s3ClientConfig.endpoint = config.endpointUrl
    s3ClientConfig.forcePathStyle = true
  }

  return { client: new S3Client(s3ClientConfig), config }
}

/**
 * 创建S3客户端（用于测试连接，可以使用任意配置）
 */
export function createS3ClientForTesting(config: S3Config) {
  const s3ClientConfig: any = {
    region: config.regionName,
    credentials: {
      accessKeyId: config.awsAccessKeyId,
      secretAccessKey: config.awsSecretAccessKey,
    },
  }

  if (config.endpointUrl) {
    s3ClientConfig.endpoint = config.endpointUrl
    s3ClientConfig.forcePathStyle = true
  }

  return { client: new S3Client(s3ClientConfig), config }
}
