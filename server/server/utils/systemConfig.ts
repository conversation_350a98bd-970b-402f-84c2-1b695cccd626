import { prisma } from "../db/prisma"
import type { SystemConfig } from '@prisma/client'

// 系统配置键值枚举
export enum SystemConfigKey {
  SYSTEM_NAME = 'system_name',
  SYSTEM_LOGO = 'system_logo',
  SYSTEM_LOGO_URL = 'system_logo_url',
  SYSTEM_DESCRIPTION = 'system_description',
  SYSTEM_VERSION = 'system_version',
  SYSTEM_COPYRIGHT = 'system_copyright'
}

/**
 * 获取所有系统配置
 */
export async function getAllSystemConfigs(): Promise<SystemConfig[]> {
  return await prisma.systemConfig.findMany({
    orderBy: { configKey: 'asc' }
  })
}

/**
 * 获取单个系统配置
 */
export async function getSystemConfig(key: string): Promise<SystemConfig | null> {
  return await prisma.systemConfig.findUnique({
    where: { configKey: key }
  })
}

/**
 * 获取系统配置值
 */
export async function getSystemConfigValue(key: string, defaultValue: string = ''): Promise<string> {
  const config = await getSystemConfig(key)
  if (!config || config.configValue === null) {
    return defaultValue
  }
  
  return config.configValue
}

/**
 * 设置系统配置
 */
export async function setSystemConfig(
  key: string, 
  value: string | null, 
  type: string = 'string',
  description?: string
): Promise<SystemConfig> {
  return await prisma.systemConfig.upsert({
    where: { configKey: key },
    update: {
      configValue: value,
      configType: type,
      description: description,
      updatedAt: new Date()
    },
    create: {
      configKey: key,
      configValue: value,
      configType: type,
      description: description
    }
  })
}

/**
 * 批量设置系统配置
 */
export async function setSystemConfigs(configs: Array<{
  key: string
  value: string | null
  type?: string
  description?: string
}>): Promise<SystemConfig[]> {
  const results: SystemConfig[] = []
  
  for (const config of configs) {
    const result = await setSystemConfig(
      config.key,
      config.value,
      config.type || 'string',
      config.description
    )
    results.push(result)
  }
  
  return results
}

/**
 * 删除系统配置
 */
export async function deleteSystemConfig(key: string): Promise<boolean> {
  try {
    await prisma.systemConfig.delete({
      where: { configKey: key }
    })
    return true
  } catch (error) {
    return false
  }
}

/**
 * 获取系统信息
 */
export async function getSystemInfo(): Promise<{
  name: string
  logo: string
  logoUrl: string
  description: string
  version: string
  copyright: string
}> {
  const configs = await getAllSystemConfigs()
  const configMap = new Map(configs.map(c => [c.configKey, c.configValue || '']))
  
  return {
    name: configMap.get(SystemConfigKey.SYSTEM_NAME) || 'Admin System',
    logo: configMap.get(SystemConfigKey.SYSTEM_LOGO) || 'A',
    logoUrl: configMap.get(SystemConfigKey.SYSTEM_LOGO_URL) || '',
    description: configMap.get(SystemConfigKey.SYSTEM_DESCRIPTION) || '现代化的后台管理系统',
    version: configMap.get(SystemConfigKey.SYSTEM_VERSION) || '1.0.0',
    copyright: configMap.get(SystemConfigKey.SYSTEM_COPYRIGHT) || '© 2024 Admin System. All rights reserved.'
  }
}

/**
 * 初始化默认系统配置
 */
export async function initDefaultSystemConfigs(): Promise<void> {
  const defaultConfigs = [
    {
      key: SystemConfigKey.SYSTEM_NAME,
      value: 'Admin System',
      type: 'string',
      description: '系统名称'
    },
    {
      key: SystemConfigKey.SYSTEM_LOGO,
      value: 'A',
      type: 'string',
      description: '系统Logo文字'
    },
    {
      key: SystemConfigKey.SYSTEM_LOGO_URL,
      value: '',
      type: 'string',
      description: '系统Logo图片URL'
    },
    {
      key: SystemConfigKey.SYSTEM_DESCRIPTION,
      value: '现代化的后台管理系统',
      type: 'string',
      description: '系统描述'
    },
    {
      key: SystemConfigKey.SYSTEM_VERSION,
      value: '1.0.0',
      type: 'string',
      description: '系统版本'
    },
    {
      key: SystemConfigKey.SYSTEM_COPYRIGHT,
      value: '© 2024 Admin System. All rights reserved.',
      type: 'string',
      description: '版权信息'
    }
  ]

  for (const config of defaultConfigs) {
    const existing = await getSystemConfig(config.key)
    if (!existing) {
      await setSystemConfig(config.key, config.value, config.type, config.description)
    }
  }
}
