import { useApi, type ApiResponse } from './useApi'
import type { PaginationParams } from './useUserApi'

// 框架状态枚举
export type FrameworkStatus = 'ENABLED' | 'COMING_SOON' | 'DISABLED'

// 框架类型定义
export interface Framework {
  id: string
  name: string
  description?: string
  status: FrameworkStatus
  introduction?: string
  sortOrder: number
  createdAt: string
  updatedAt: string
}

// 创建框架参数
export interface CreateFrameworkParams {
  name: string
  description?: string
  status?: FrameworkStatus
  introduction?: string
  sortOrder?: number
}

// 更新框架参数
export interface UpdateFrameworkParams {
  name?: string
  description?: string
  status?: FrameworkStatus
  introduction?: string
  sortOrder?: number
}

// 框架筛选参数
export interface FrameworkFilterParams {
  search?: string
  status?: FrameworkStatus
}

// 分页查询参数
export interface FrameworkListParams extends FrameworkFilterParams, PaginationParams {}

// 框架分页响应
export interface FrameworkPaginatedResponse {
  data: Framework[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
  success: boolean
}

// 框架统计
export interface FrameworkStats {
  total: number
  enabled: number
  comingSoon: number
  disabled: number
}



/**
 * 框架 API 服务
 */
export const useFrameworkApi = () => {
  const api = useApi()

  /**
   * 分页获取框架列表
   */
  const getFrameworks = (params: FrameworkListParams = {}) => {
    const queryParams = new URLSearchParams()

    if (params.page) queryParams.append('page', params.page.toString())
    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString())
    if (params.search) queryParams.append('search', params.search)
    if (params.status) queryParams.append('status', params.status)

    const queryString = queryParams.toString()
    const url = queryString ? `/api/admin/frameworks?${queryString}` : '/api/admin/frameworks'

    return api.get<FrameworkPaginatedResponse>(url)
  }

  /**
   * 获取框架统计信息
   */
  const getFrameworkStats = () => {
    return api.get<ApiResponse<FrameworkStats>>('/api/admin/frameworks/stats')
  }

  /**
   * 根据ID获取框架详情
   */
  const getFrameworkById = (id: string) => {
    return api.get<ApiResponse<Framework>>(`/api/admin/frameworks/${id}`)
  }

  /**
   * 创建框架
   */
  const createFramework = (data: CreateFrameworkParams) => {
    return api.post<ApiResponse<Framework>>('/api/admin/frameworks', data)
  }

  /**
   * 更新框架
   */
  const updateFramework = (id: string, data: UpdateFrameworkParams) => {
    return api.put<ApiResponse<Framework>>(`/api/admin/frameworks/${id}`, data)
  }

  /**
   * 删除框架
   */
  const deleteFramework = (id: string) => {
    return api.delete<ApiResponse<void>>(`/api/admin/frameworks/${id}`)
  }

  /**
   * 批量更新框架排序
   */
  const updateFrameworksOrder = (orders: { id: string; sortOrder: number }[]) => {
    return api.put<ApiResponse<void>>('/api/admin/frameworks/order', { orders })
  }

  /**
   * 获取公开的框架列表 (供前端web调用)
   */
  const getPublicFrameworks = (status?: FrameworkStatus) => {
    const params = new URLSearchParams()
    if (status) params.append('status', status)

    const url = params.toString() ? `/api/frameworks?${params}` : '/api/frameworks'
    return api.get<{ success: boolean; data: Framework[] }>(url, { requireAuth: false })
  }

  return {
    getFrameworks,
    getFrameworkStats,
    getFrameworkById,
    createFramework,
    updateFramework,
    deleteFramework,
    updateFrameworksOrder,
    getPublicFrameworks, // 新增公开接口
  }
}


