<template>
  <div class="container mx-auto p-4">
    <div class="max-w-7xl mx-auto">
      <h1 class="text-2xl md:text-3xl font-bold mb-6">框架管理</h1>

      <Card>
        <CardHeader>
          <div class="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div>
              <CardTitle class="text-lg md:text-xl">框架列表</CardTitle>
              <CardDescription class="text-sm">管理开发框架和模板</CardDescription>
            </div>
            <Button @click="showCreateModal = true" class="gap-2 w-full md:w-auto">
              <Plus class="h-4 w-4" />
              添加框架
            </Button>
          </div>
        </CardHeader>
        <CardContent>

    <!-- 搜索和筛选 -->
    <div class="mb-6 flex flex-col sm:flex-row gap-4">
      <!-- 搜索 -->
      <div class="relative">
        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">🔍</span>
        <input
          v-model="searchTerm"
          type="text"
          placeholder="搜索框架..."
          class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          @input="debouncedSearch"
        />
      </div>

      <!-- 状态筛选 -->
      <select
        v-model="selectedStatus"
        class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        @change="loadFrameworks"
      >
        <option value="">所有状态</option>
        <option value="ENABLED">启用</option>
        <option value="COMING_SOON">即将推出</option>
        <option value="DISABLED">禁用</option>
      </select>
    </div>

    <!-- 统计信息 -->
    <div v-if="stats" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-white p-4 rounded-lg border border-gray-200">
        <div class="text-2xl font-bold text-gray-900">{{ stats.total }}</div>
        <div class="text-sm text-gray-600">总框架数</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-gray-200">
        <div class="text-2xl font-bold text-green-600">{{ stats.enabled }}</div>
        <div class="text-sm text-gray-600">启用框架</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-gray-200">
        <div class="text-2xl font-bold text-yellow-600">{{ stats.comingSoon }}</div>
        <div class="text-sm text-gray-600">即将推出</div>
      </div>
      <div class="bg-white p-4 rounded-lg border border-gray-200">
        <div class="text-2xl font-bold text-gray-600">{{ stats.disabled }}</div>
        <div class="text-sm text-gray-600">禁用框架</div>
      </div>
    </div>

    <!-- 框架列表 -->
    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <div v-if="loading" class="flex items-center justify-center h-64">
        <div class="text-center">
          <div class="w-8 h-8 border-2 border-blue-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p class="text-gray-600">加载中...</p>
        </div>
      </div>

      <div v-else-if="frameworks.length === 0" class="text-center py-12">
        <div class="text-4xl text-gray-400 mx-auto mb-4">📦</div>
        <p class="text-gray-500">没有找到匹配的框架</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                框架信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                排序
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                创建时间
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="framework in frameworks" :key="framework.id" class="hover:bg-gray-50">
              <td class="px-6 py-4">
                <div>
                  <div class="text-sm font-medium text-gray-900">
                    {{ framework.name }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ framework.description }}
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusClass(framework.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ getStatusLabel(framework.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ framework.sortOrder }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(framework.createdAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <button
                    @click="editFramework(framework)"
                    class="p-1 text-blue-600 hover:bg-blue-50 rounded"
                    title="编辑"
                  >
                    ✏️
                  </button>
                  <button
                    @click="deleteFramework(framework)"
                    class="p-1 text-red-600 hover:bg-red-50 rounded"
                    title="删除"
                  >
                    🗑️
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div v-if="pagination && pagination.totalPages > 1" class="px-6 py-3 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示 {{ (pagination.page - 1) * pagination.pageSize + 1 }} 到 
            {{ Math.min(pagination.page * pagination.pageSize, pagination.total) }} 
            共 {{ pagination.total }} 条
          </div>
          <div class="flex space-x-2">
            <button
              :disabled="pagination.page <= 1"
              @click="changePage(pagination.page - 1)"
              class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <button
              :disabled="pagination.page >= pagination.totalPages"
              @click="changePage(pagination.page + 1)"
              class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑模态框 -->
    <div v-if="showCreateModal || editingFramework" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <h3 class="text-lg font-medium mb-4">
          {{ editingFramework ? '编辑框架' : '添加框架' }}
        </h3>
        
        <form @submit.prevent="saveFramework">
          <div class="space-y-4">
            <!-- 框架名称 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                框架名称 <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="如：Next.js"
              />
            </div>

            <!-- 框架描述 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                框架描述
              </label>
              <textarea
                v-model="formData.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="简要描述框架的特点和用途"
              ></textarea>
            </div>

            <!-- 框架状态 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                框架状态
              </label>
              <select
                v-model="formData.status"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="ENABLED">启用</option>
                <option value="COMING_SOON">即将推出</option>
                <option value="DISABLED">禁用</option>
              </select>
            </div>

            <!-- 框架介绍 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                框架介绍
              </label>
              <textarea
                v-model="formData.introduction"
                rows="4"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="详细介绍框架，可包含视频链接等"
              ></textarea>
            </div>

            <!-- 排序权重 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                排序权重
              </label>
              <input
                v-model.number="formData.sortOrder"
                type="number"
                min="0"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="数字越小越靠前"
              />
            </div>
          </div>

          <div class="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              @click="closeModal"
              class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="saving"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {{ saving ? '保存中...' : '保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '~/components/ui/card'
import { Plus } from 'lucide-vue-next'
// useFrameworkApi 会被 Nuxt 自动导入，无需手动导入
// 页面元数据
definePageMeta({
  layout: 'default'
})

// 使用框架API
const frameworkApi = useFrameworkApi()

// 响应式数据
const frameworks = ref([])
const stats = ref({
  total: 0,
  enabled: 0,
  comingSoon: 0,
  disabled: 0
})
const loading = ref(true)
const saving = ref(false)
const searchTerm = ref('')
const selectedStatus = ref('')
const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
})
const currentPage = ref(1)

// 模态框状态
const showCreateModal = ref(false)
const editingFramework = ref(null)
const formData = ref({
  name: '',
  description: '',
  status: 'ENABLED',
  introduction: '',
  sortOrder: 0
})

// 加载框架列表
const loadFrameworks = async () => {
  try {
    loading.value = true

    const params = {
      page: currentPage.value,
      pageSize: 10,
      ...(selectedStatus.value && { status: selectedStatus.value }),
      ...(searchTerm.value && { search: searchTerm.value })
    }

    const response = await frameworkApi.getFrameworks(params)
    if (response.success) {
      frameworks.value = response.data || []
      pagination.value = response.pagination || { page: 1, pageSize: 10, total: 0, totalPages: 0 }
    }
  } catch (error) {
    console.error('加载框架列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载统计信息
const loadStats = async () => {
  try {
    const response = await frameworkApi.getFrameworkStats()
    if (response.success) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

// 防抖搜索
let searchTimeout = null
const debouncedSearch = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    currentPage.value = 1
    loadFrameworks()
  }, 500)
}

// 分页
const changePage = (page) => {
  currentPage.value = page
  loadFrameworks()
}

// 编辑框架
const editFramework = (framework) => {
  editingFramework.value = framework
  formData.value = {
    name: framework.name,
    description: framework.description || '',
    status: framework.status,
    introduction: framework.introduction || '',
    sortOrder: framework.sortOrder
  }
}

// 删除框架
const deleteFramework = async (framework) => {
  if (!confirm(`确定要删除框架 "${framework.name}" 吗？`)) {
    return
  }

  try {
    await frameworkApi.deleteFramework(framework.id)
    await loadFrameworks()
    await loadStats()
  } catch (error) {
    alert('删除失败: ' + (error.data?.error || error.message))
  }
}

// 保存框架
const saveFramework = async () => {
  try {
    saving.value = true

    if (editingFramework.value) {
      // 更新
      await frameworkApi.updateFramework(editingFramework.value.id, formData.value)
    } else {
      // 创建
      await frameworkApi.createFramework(formData.value)
    }

    closeModal()
    await loadFrameworks()
    await loadStats()
  } catch (error) {
    alert('保存失败: ' + (error.data?.error || error.message))
  } finally {
    saving.value = false
  }
}

// 关闭模态框
const closeModal = () => {
  showCreateModal.value = false
  editingFramework.value = null
  formData.value = {
    name: '',
    description: '',
    status: 'ENABLED',
    introduction: '',
    sortOrder: 0
  }
}

// 状态样式
const getStatusClass = (status) => {
  const classes = {
    'ENABLED': 'text-green-800 bg-green-100',
    'COMING_SOON': 'text-yellow-800 bg-yellow-100',
    'DISABLED': 'text-gray-800 bg-gray-100'
  }
  return classes[status] || 'text-gray-800 bg-gray-100'
}

// 状态标签
const getStatusLabel = (status) => {
  const labels = {
    'ENABLED': '启用',
    'COMING_SOON': '即将推出',
    'DISABLED': '禁用'
  }
  return labels[status] || status
}

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 页面加载时获取数据
onMounted(() => {
  loadFrameworks()
  loadStats()
})
</script>
