# Elysia Nuxt Admin 开发规范

## 项目概述

- 详细的项目信息请参考 README.md
- 这是一个基于 Nuxt 3 + Elysia + shadcn-vue 构建的全栈管理系统

## 目录结构规范

### 前端开发 (Nuxt)

1. 页面目录 (`/pages`)
   - 所有新页面必须放在 `/pages` 目录下
   - 文件名使用短横线命名法: `user-profile.vue`
   - 相关页面应按功能分组放在子目录中: `/pages/userManager/`
   - 每个页面组件应职责明确
   - 需要时在 script setup 中包含路由元信息

2. 组件目录 (`/components`)
   - UI 组件放在 `/components/ui`
   - 布局组件放在 `/components/layout`
   - 功能组件放在以功能命名的目录中
   - 组件名使用大驼峰命名法: `UserList.vue`
   - 保持组件精简，功能聚焦

3. 组合式函数目录 (`/composables`)
   - 使用组合式 API 风格
   - 文件名以 'use' 开头: `useAuth.ts`
   - 业务逻辑与 UI 分离

### 后端开发 (Elysia)

1. 路由目录 (`/server/routes`)
   - 每个功能域一个路由文件
   - 所有端点必须包含 Swagger 文档
   - 遵循 RESTful 规范
   - 相关端点应在同一文件中分组

2. 服务目录 (`/server/services`)
   - 存放业务逻辑
   - 每个领域一个服务
   - 保持服务职责单一
   - 所有方法使用 TypeScript 接口

3. 数据库目录 (`/server/db`)
   - 架构变更需要更新文档
   - 包含数据验证
   - 使用预处理语句进行查询
   - 数据库逻辑保持在服务层

## 编码标准

### TypeScript 规范

- 启用严格类型检查
- 除非绝对必要，否则不使用 `any` 类型
- 优先使用接口而不是类型
- 为复杂类型编写文档

### Vue 组件规范

- 使用 script setup 语法
- 使用运行时验证定义 props
- 使用组合式函数共享逻辑
- 保持模板逻辑简单

### API 开发规范

- 所有端点必须有 Swagger 文档
- 使用正确的 HTTP 状态码
- 验证所有输入
- 统一错误处理

## 状态管理

- 简单状态使用组合式函数
- 记录状态管理决策
- 状态尽可能靠近使用处

## 测试指南

- 为关键路径编写测试
- 测试成功和失败场景
- 模拟外部依赖
- 保持测试重点明确

## 错误处理

- 使用自定义错误类
- 适当记录错误日志
- 提供友好的错误信息
- 处理边缘情况

## 性能指南

- 路由和组件懒加载
- 优化数据库查询
- 使用适当的缓存策略
- 监控打包大小

## 安全规则

- 验证所有输入
- 使用正确的认证方式
- 防止 SQL 注入
- 遵循安全最佳实践

## Git 工作流

- 使用功能分支
- 编写有意义的提交信息
- 保持 PR 精简且重点明确
- 合并前进行代码审查

## 文档规范

- 重大更改需更新 README.md
- 记录 API 变更到 Swagger
- 保持内联文档最新
- 记录配置变更

## 开发流程

1. 创建功能分支
2. 实现更改
3. 添加/更新测试
4. 更新文档
5. 创建拉取请求
6. 代码审查
7. 审批后合并

## 文件命名规范

- Vue 组件：大驼峰命名法.vue
- TypeScript 文件：短横线命名法.ts
- 路由文件：短横线命名法.ts
- 测试文件：*.spec.ts 或*.test.ts

## 导入顺序

1. 外部库
2. 内部模块
3. 组件
4. 类型/接口
5. 样式/资源

## 环境配置

- 正确使用 .env 文件
- 记录必需的环境变量
- 保护敏感数据安全
- 使用正确的配置管理

### UI 组件规范 (shadcn-vue)

1. 组件选择
   - 优先使用 shadcn-vue 提供的组件
   - 常用组件参考：
     - `Button` - 所有按钮场景
     - `Input` - 输入框
     - `Select` - 下拉选择
     - `Table` - 数据表格
     - `Card` - 卡片布局
     - `Dialog` - 对话框
     - `Form` - 表单布局
     - `Alert` - 提示信息
     - `Dropdown` - 下拉菜单
     - `Tabs` - 标签页
     - `Toast` - 消息提示

2. 组件定制
   - 使用 Tailwind CSS 进行样式调整
   - 遵循项目调色板
   - 保持组件风格统一
   - 避免直接修改组件源码

3. 组件复用
   - 对于重复使用的组件组合，创建复合组件
   - 将复合组件放在 `components/composite` 目录
   - 为复合组件编写清晰的 Props 定义
   - 保持复合组件的可配置性

4. 最佳实践
   - 使用组件的 TypeScript 类型
   - 遵循组件文档的推荐用法
   - 保持 props 命名一致性
   - 使用组件的插槽功能进行定制

5. 性能考虑
   - 合理使用动态组件
   - 必要时使用组件懒加载
   - 避免不必要的组件嵌套
   - 注意组件的重渲染优化

请参考 README.md 获取项目设置和其他详细信息。
