# AI开发平台 - Web IDE

基于AI驱动的智能开发平台，支持实时代码生成、容器化开发环境和智能对话。

## 🚀 技术栈

### 前端 (Next.js)
- **框架**: Next.js 15.2+ (React 18+)
- **UI库**: shadcn/ui + Tailwind CSS
- **语言**: TypeScript
- **端口**: 5173

### 后端 (Elysia)
- **框架**: Elysia + Bun.js
- **数据库**: PostgreSQL + Prisma ORM
- **认证**: JWT
- **端口**: 3000

## 📦 项目结构

```
vibe-coding-dev/
├── web/                    # Next.js前端应用
│   ├── app/               # Next.js App Router
│   ├── components/        # React组件
│   ├── lib/              # 工具函数
│   └── package.json      # 前端依赖
├── server/               # Elysia后端服务
│   ├── server/           # 服务器代码
│   ├── prisma/           # 数据库模式
│   └── package.json      # 后端依赖
└── docs/                 # 项目文档
```

## 🛠️ 开发环境设置

### 1. 环境要求
- Node.js 18+
- Bun.js 1.0+
- PostgreSQL 15+
- pnpm (推荐)

### 2. 数据库配置
```bash
# PostgreSQL连接信息
Host: *************
Port: 5433
Database: vibe-coding-dev
Username: vibe-coding-dev
Password: 7pDkn4kx7jWjJR7G
```

### 3. 启动后端服务
```bash
cd server
pnpm install
bun run server
```
后端服务将在 http://localhost:3000 启动

### 4. 启动前端服务
```bash
cd web
pnpm install
pnpm dev
```
前端服务将在 http://localhost:5173 启动

## 🔧 API端点

### 认证相关
- `POST /api/users/login` - 用户登录
- `POST /api/users/register` - 用户注册
- `GET /api/users/profile` - 获取用户信息

### 应用管理
- `GET /api/applications` - 获取应用列表
- `POST /api/applications` - 创建新应用
- `GET /api/applications/:id` - 获取应用详情
- `PUT /api/applications/:id` - 更新应用
- `DELETE /api/applications/:id` - 删除应用

### AI对话
- `POST /api/conversations` - 发送AI消息
- `GET /api/conversations/application/:id` - 获取对话历史

### 容器控制
- `POST /api/applications/:id/container/start` - 启动容器
- `POST /api/applications/:id/container/stop` - 停止容器
- `POST /api/applications/:id/container/restart` - 重启容器

## 🎯 核心功能

### 1. 用户认证
- JWT令牌认证
- 用户注册/登录
- 权限管理

### 2. AI应用管理
- 创建AI开发项目
- 应用状态管理
- 容器生命周期控制

### 3. AI智能对话
- 实时AI代码生成
- 多种消息类型支持
- 对话历史记录

### 4. 开发环境
- Docker容器化
- 代码实时同步
- S3对象存储

## 🔐 默认账号

```
用户名: admin
密码: Admin@123
```

## 📝 开发说明

### CORS配置
后端已配置CORS支持以下前端域名：
- http://localhost:5173 (Next.js开发服务器)
- http://localhost:5174 (备用端口)
- http://localhost:3001 (其他服务)
- http://localhost:3000 (Next.js生产端口)

### API前缀
所有API端点都使用 `/api` 前缀，例如：
- 前端请求: `http://localhost:3000/api/users/login`
- 后端路由: `/api/users/login`

### 数据库模式
使用Prisma ORM管理数据库，主要模型包括：
- `User` - 用户信息
- `Application` - AI应用
- `Container` - 容器信息
- `Conversation` - AI对话记录

## 🚀 部署

### 开发环境
1. 启动后端: `cd server && bun run server`
2. 启动前端: `cd web && pnpm dev`
3. 访问: http://localhost:5173

### 生产环境
详见 `docs/architecture.md` 中的部署配置。

## 📚 文档

- [系统架构](./docs/architecture.md) - 详细的技术架构说明
- [需求文档](./docs/requirements.md) - 项目需求和功能说明

## 🤝 贡献

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License
