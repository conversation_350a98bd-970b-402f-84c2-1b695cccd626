# Web IDE 平台需求文档

## 项目概述

开发一个基于Web的集成开发环境(IDE)，支持AI辅助开发和传统代码编辑，用户可以通过对话生成代码或手动编写代码，并一键发布到生产环境。

## 技术栈

### 前端

- Vue 3 + TypeScript
- 代码编辑器组件（Monaco Editor 或 CodeMirror）
- WebSocket 客户端
- 构建工具：Vite

### 后端

- Elysia (Bun.js 框架)
- Prisma ORM
- PostgreSQL 数据库
- WebSocket 支持

### 容器服务

- Docker 容器环境
- NestJS 预设框架
- AI 服务集成
- WebSocket 服务

### 第三方服务

- Docker 容器服务 API
- AI 代码生成服务
- 对象存储服务
- Redis 缓存

## 核心功能模块

### 1. 用户认证与管理

- 用户注册/登录
- 用户权限管理
- 账户设置

### 2. 项目管理

- 创建项目
- 项目列表
- 项目配置
- 项目删除

### 3. 开发环境管理

- Docker 容器创建
- 数据库初始化
- Redis 配置
- 对象存储配置

### 4. AI 辅助开发

- 自然语言对话界面
- 代码生成请求
- 生成结果预览
- 代码应用确认

### 5. 代码编辑器

- 语法高亮
- 代码补全
- 文件树管理
- 多文件编辑

### 6. 实时预览

- 前端应用预览
- API 接口测试
- 数据库数据查看
- 日志查看

### 7. 部署管理

- 代码打包
- Docker 镜像构建
- 生产环境部署
- 部署状态监控

## 系统架构

### 整体架构

```
[客户端Web] <-> [服务端API] <-> [第三方Docker服务]
     |              |                    |
     |              |                    v
     |              |            [容器内服务]
     |              |                    |
     |              v                    |
     |        [PostgreSQL]               |
     |              |                    |
     v              v                    v
[WebSocket] <-> [Redis] <----------> [AI服务]
```

### 数据流

1. 用户在客户端创建项目
2. 服务端调用第三方API创建Docker容器
3. 容器内初始化开发环境和AI服务
4. 客户端通过WebSocket与容器通信
5. AI对话通过容器内AI服务处理
6. 代码修改实时同步到容器
7. 预览通过容器内服务提供
8. 部署时打包容器内代码

## 详细功能需求

### 客户端Web应用

#### 用户界面布局

- 顶部导航栏：用户信息、项目切换
- 左侧面板：文件树、项目管理
- 中央区域：代码编辑器/AI对话界面
- 右侧面板：预览窗口、数据库查看
- 底部状态栏：连接状态、操作日志

#### 核心页面

1. **登录页面**
   - 用户名/密码登录
   - 第三方登录（可选）
   - 注册链接

2. **项目管理页面**
   - 项目列表展示
   - 创建新项目按钮
   - 项目搜索和筛选
   - 项目设置入口

3. **IDE主界面**
   - 文件管理器
   - 代码编辑器
   - AI对话面板
   - 预览窗口
   - 终端窗口

#### 交互功能

- 文件的增删改查
- 代码实时保存
- AI对话历史记录
- 快捷键支持
- 拖拽文件操作

### 服务端API

#### 用户管理API

- POST /auth/register - 用户注册
- POST /auth/login - 用户登录
- GET /auth/profile - 获取用户信息
- PUT /auth/profile - 更新用户信息

#### 项目管理API

- GET /projects - 获取项目列表
- POST /projects - 创建项目
- GET /projects/:id - 获取项目详情
- PUT /projects/:id - 更新项目
- DELETE /projects/:id - 删除项目

#### 容器管理API

- POST /containers - 创建容器
- GET /containers/:id - 获取容器状态
- DELETE /containers/:id - 删除容器
- POST /containers/:id/deploy - 部署到生产

#### WebSocket API

- 文件操作事件
- AI对话事件
- 预览更新事件
- 状态同步事件

### 容器服务

#### WebSocket 服务

- 与客户端建立连接
- 处理文件操作请求
- 转发AI对话请求
- 推送状态更新

#### 文件管理服务

- 目录结构管理
- 文件内容读写
- 文件权限控制
- 版本控制集成

#### AI 代码生成服务

- 接收自然语言需求
- 调用AI模型生成代码
- 代码质量检查
- 生成结果返回

#### 预设开发框架

- NestJS 后端模板
- Vue/React 前端模板
- 数据库迁移脚本
- 基础配置文件

## 数据库设计

### 用户表 (users)

- id: 主键
- username: 用户名
- email: 邮箱
- password_hash: 密码哈希
- created_at: 创建时间
- updated_at: 更新时间

### 项目表 (projects)

- id: 主键
- user_id: 用户ID (外键)
- name: 项目名称
- description: 项目描述
- container_id: 容器ID
- status: 项目状态
- created_at: 创建时间
- updated_at: 更新时间

### 容器表 (containers)

- id: 主键
- project_id: 项目ID (外键)
- docker_id: Docker容器ID
- status: 容器状态
- config: 容器配置 (JSON)
- created_at: 创建时间
- updated_at: 更新时间

### AI对话记录表 (ai_conversations)

- id: 主键
- project_id: 项目ID (外键)
- user_message: 用户消息
- ai_response: AI回复
- generated_code: 生成的代码
- applied: 是否已应用
- created_at: 创建时间

## 非功能性需求

### 性能要求

- 页面加载时间 < 3秒
- 代码编辑响应时间 < 100ms
- AI代码生成时间 < 30秒
- 支持并发用户数 > 100

### 安全要求

- 用户数据加密存储
- API接口认证授权
- 容器间隔离
- 代码访问权限控制

### 可用性要求

- 系统可用性 > 99%
- 数据备份策略
- 容错处理机制
- 监控告警系统

## 开发里程碑

### 阶段一：基础架构 (4周)

- 项目初始化
- 数据库设计实现
- 基础API开发
- 用户认证系统

### 阶段二：核心功能 (6周)

- 项目管理功能
- 容器服务开发
- WebSocket通信
- 文件管理系统

### 阶段三：AI集成 (4周)

- AI服务集成
- 对话界面开发
- 代码生成功能
- 预览系统

### 阶段四：完善优化 (3周)

- 代码编辑器集成
- 部署功能开发
- 性能优化
- 测试完善

### 阶段五：上线准备 (2周)

- 生产环境部署
- 监控系统搭建
- 文档完善
- 用户培训

## 风险评估

### 技术风险

- Docker容器服务稳定性
- AI服务响应时间
- WebSocket连接稳定性
- 大文件处理性能

### 业务风险

- 用户接受度
- 竞品压力
- 成本控制
- 扩展性需求

## 后续扩展

### 功能扩展

- 多人协作开发
- 版本控制集成
- 插件系统
- 移动端支持

### 技术扩展

- 微服务架构
- 容器编排
- 边缘计算
- 多云部署
