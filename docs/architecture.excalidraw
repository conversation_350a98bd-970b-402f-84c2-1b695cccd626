{"type": "excalidraw", "version": 2, "source": "https://marketplace.visualstudio.com/items?itemName=pomdtr.excalidraw-editor", "elements": [{"id": "database-box", "type": "rectangle", "x": 370.205078125, "y": 46.953125, "width": 142.9525146484375, "height": 80, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffec99", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "seed": 1, "versionNonce": 1829449270, "isDeleted": false, "boundElements": [{"id": "server-db-arrow", "type": "arrow"}], "updated": 1751646712527, "link": null, "locked": false, "version": 203, "index": "Zz"}, {"id": "database-text", "type": "text", "x": 381.69921875, "y": 56.03515625, "width": 72, "height": 60, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 2, "versionNonce": 1966987690, "isDeleted": false, "boundElements": [{"id": "server-db-arrow", "type": "arrow"}], "updated": 1751646707382, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "PostgreSQL\n(主数据库)\n\n仅服务端访问", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "PostgreSQL\n(主数据库)\n\n仅服务端访问", "lineHeight": 1.25, "baseline": 34, "index": "a0", "autoResize": true, "version": 130}, {"id": "client-box", "type": "rectangle", "x": 6.11651611328125, "y": 198.55795288085938, "width": 180, "height": 120, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "seed": 3, "versionNonce": 1251529910, "isDeleted": false, "boundElements": [{"id": "client-server-arrow", "type": "arrow"}], "updated": 1751646578308, "link": null, "locked": false, "version": 250, "index": "a0V"}, {"id": "client-text", "type": "text", "x": 41.44209289550781, "y": 205.849609375, "width": 129.76885986328125, "height": 105, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 4, "versionNonce": 463050166, "isDeleted": false, "boundElements": [], "updated": 1751646584713, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "客户端Web\n(Vue3 + TS)\n\n• 用户界面\n• 代码编辑器\n• AI对话界面\n• 实时预览", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "客户端Web\n(Vue3 + TS)\n\n• 用户界面\n• 代码编辑器\n• AI对话界面\n• 实时预览", "lineHeight": 1.25, "baseline": 74, "index": "a1", "autoResize": false, "version": 254}, {"id": "server-box", "type": "rectangle", "x": 347.0181884765625, "y": 201.55599975585938, "width": 200, "height": 120, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "seed": 5, "versionNonce": 1500432502, "isDeleted": false, "boundElements": [{"id": "client-server-arrow", "type": "arrow"}, {"id": "server-third-party-arrow", "type": "arrow"}, {"id": "server-websocket-arrow", "type": "arrow"}, {"id": "server-db-arrow", "type": "arrow"}], "updated": 1751646783421, "link": null, "locked": false, "version": 132, "index": "a1V"}, {"id": "server-text", "type": "text", "x": 370.09442138671875, "y": 216.0513916015625, "width": 88.68224099136535, "height": 98.3984375, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 6, "versionNonce": 654563818, "isDeleted": false, "boundElements": [{"id": "server-websocket-arrow", "type": "arrow"}, {"id": "server-db-arrow", "type": "arrow"}], "updated": 1751646796406, "link": null, "locked": false, "fontSize": 11.245535714285715, "fontFamily": 1, "text": "服务端API\n(Elysia + TS)\n\n• 用户管理\n• 项目管理\n• 容器编排\n• WebSocket中继", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "服务端API\n(Elysia + TS)\n\n• 用户管理\n• 项目管理\n• 容器编排\n• WebSocket中继", "lineHeight": 1.25, "baseline": 74, "index": "a2", "autoResize": true, "version": 201}, {"id": "third-party-box", "type": "rectangle", "x": 658.7760620117188, "y": 202.8353271484375, "width": 180, "height": 120, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd43b", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "seed": 7, "versionNonce": 2032257654, "isDeleted": false, "boundElements": [{"id": "server-third-party-arrow", "type": "arrow"}, {"id": "third-party-project-db-arrow", "type": "arrow"}, {"id": "third-party-redis-arrow", "type": "arrow"}, {"id": "third-party-container-arrow", "type": "arrow"}], "updated": 1751646617886, "link": null, "locked": false, "version": 58, "index": "a2V"}, {"id": "third-party-text", "type": "text", "x": 684.1211547851562, "y": 211.796875, "width": 76.97991943359375, "height": 105, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 8, "versionNonce": 2137741034, "isDeleted": false, "boundElements": [], "updated": 1751646605614, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "第三方服务\n(Docker API)\n\n• 容器创建\n• 镜像构建\n• 部署管理\n• 资源监控", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "第三方服务\n(Docker API)\n\n• 容器创建\n• 镜像构建\n• 部署管理\n• 资源监控", "lineHeight": 1.25, "baseline": 74, "index": "a3", "autoResize": true, "version": 76}, {"id": "websocket-service-box", "type": "rectangle", "x": 142.63995361328125, "y": 362.36334228515625, "width": 180, "height": 122.71160888671872, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#e599f7", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "seed": 9, "versionNonce": 789935594, "isDeleted": false, "boundElements": [{"id": "server-websocket-arrow", "type": "arrow"}, {"id": "websocket-container-arrow", "type": "arrow"}], "updated": 1751646817339, "link": null, "locked": false, "version": 235, "index": "a3V"}, {"id": "websocket-service-text", "type": "text", "x": 165.625, "y": 383.16412353515625, "width": 84.7679443359375, "height": 90, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 10, "versionNonce": 540402282, "isDeleted": false, "boundElements": [], "updated": 1751646813371, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "WebSocket服务\n(独立服务)\n\n• 服务端通信\n• 容器协调\n• 消息转发", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "WebSocket服务\n(独立服务)\n\n• 服务端通信\n• 容器协调\n• 消息转发", "lineHeight": 1.25, "baseline": 54, "index": "a4", "autoResize": true, "version": 50}, {"id": "docker-container-box", "type": "rectangle", "x": 95.56967163085938, "y": 580.1627197265625, "width": 200, "height": 120, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "seed": 11, "versionNonce": 929309686, "isDeleted": false, "boundElements": [{"id": "third-party-container-arrow", "type": "arrow"}, {"id": "websocket-container-arrow", "type": "arrow"}], "updated": 1751646665913, "link": null, "locked": false, "version": 31, "index": "a4V"}, {"id": "docker-container-text", "type": "text", "x": 109.67123413085938, "y": 600.15625, "width": 110.18391418457031, "height": 90, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 12, "versionNonce": 1742282730, "isDeleted": false, "boundElements": [], "updated": 1751646673089, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "Docker容器\n\n• NestJS预设框架\n• Claude Code CLI\n• 文件管理系统\n• 内部服务协调", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Docker容器\n\n• NestJS预设框架\n• Claude Code CLI\n• 文件管理系统\n• 内部服务协调", "lineHeight": 1.25, "baseline": 74, "index": "a5", "autoResize": true, "version": 3}, {"id": "project-db-box", "type": "rectangle", "x": 400, "y": 580, "width": 150, "height": 80, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffec99", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "seed": 13, "versionNonce": 643807018, "isDeleted": false, "boundElements": [{"id": "third-party-project-db-arrow", "type": "arrow"}], "updated": 1751646610890, "link": null, "locked": false, "version": 3, "index": "a5V"}, {"id": "project-db-text", "type": "text", "x": 410.68035888671875, "y": 590.7877807617188, "width": 121.35586547851562, "height": 60, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 14, "versionNonce": 424477162, "isDeleted": false, "boundElements": [], "updated": 1751646676739, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "项目专用数据库\n(PostgreSQL/MySQL)\n\n独立实例", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "项目专用数据库\n(PostgreSQL/MySQL)\n\n独立实例", "lineHeight": 1.25, "baseline": 34, "index": "a6", "autoResize": true, "version": 21}, {"id": "redis-box", "type": "rectangle", "x": 600, "y": 580, "width": 150, "height": 80, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffec99", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "seed": 15, "versionNonce": 1082849898, "isDeleted": false, "boundElements": [{"id": "third-party-redis-arrow", "type": "arrow"}], "updated": 1751646614859, "link": null, "locked": false, "version": 3, "index": "a6V"}, {"id": "redis-text", "type": "text", "x": 637.6920166015625, "y": 589.1958618164062, "width": 72, "height": 60, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 16, "versionNonce": 68809962, "isDeleted": false, "boundElements": [], "updated": 1751646830551, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "Redis缓存\n\n项目专用实例\n高性能存储", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "Redis缓存\n\n项目专用实例\n高性能存储", "lineHeight": 1.25, "baseline": 34, "index": "a7", "autoResize": true, "version": 42}, {"id": "client-server-arrow", "type": "arrow", "x": 195.08409632908158, "y": 261.55908634569914, "width": 142.15470690677202, "height": 0.0031958413695178933, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 17, "versionNonce": 2003018486, "isDeleted": false, "boundElements": [], "updated": 1751646783421, "link": null, "locked": false, "points": [[0, 0], [142.15470690677202, -0.0031958413695178933]], "lastCommittedPoint": null, "startBinding": {"elementId": "client-box", "focus": 0.050054272165484096, "gap": 10.961032133536065}, "endBinding": {"elementId": "server-box", "focus": 4.270054558876315e-05, "gap": 11.619378854982358}, "startArrowhead": null, "endArrowhead": "arrow", "version": 704, "index": "a74"}, {"id": "server-websocket-arrow", "type": "arrow", "x": 343.18646728397084, "y": 321.04931531159946, "width": 25.896134450636794, "height": 38.0777300778019, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 18, "versionNonce": 677152298, "isDeleted": false, "boundElements": [], "updated": 1751646817355, "link": null, "locked": false, "points": [[0, 0], [-25.896134450636794, 38.0777300778019]], "lastCommittedPoint": null, "startBinding": {"elementId": "server-box", "focus": 0.4500025763805543, "gap": 13.254414308519374}, "endBinding": {"elementId": "websocket-service-box", "focus": 0.3095083000031066, "gap": 10.919698716815567}, "startArrowhead": null, "endArrowhead": "arrow", "version": 328, "index": "a78"}, {"id": "websocket-container-arrow", "type": "arrow", "x": 218.5492008865535, "y": 491.5430289518888, "width": 63.77001268089447, "height": 83.3342829254658, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 19, "versionNonce": 2147365098, "isDeleted": false, "boundElements": [], "updated": 1751646817355, "link": null, "locked": false, "points": [[0, 0], [-63.77001268089447, 83.3342829254658]], "lastCommittedPoint": null, "startBinding": {"elementId": "websocket-service-box", "focus": -0.2761428934285339, "gap": 11.10345458984375}, "endBinding": {"elementId": "docker-container-box", "focus": -0.6219347406782982, "gap": 9.8502197265625}, "startArrowhead": null, "endArrowhead": "arrow", "version": 277, "index": "a7G"}, {"id": "server-db-arrow", "type": "arrow", "x": 449.48013665970757, "y": 194.08856201171878, "width": 0.09699727678082581, "height": 61.883449389832094, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 24, "versionNonce": 1043354090, "isDeleted": false, "boundElements": [], "updated": 1751646789545, "link": null, "locked": false, "points": [[0, 0], [-0.09699727678082581, -61.883449389832094]], "lastCommittedPoint": null, "startBinding": {"elementId": "server-box", "focus": 0.025652853819903496, "gap": 7.467437744140597}, "endBinding": {"elementId": "database-box", "focus": -0.1066674124646036, "gap": 5.251987621886684}, "startArrowhead": null, "endArrowhead": "triangle", "version": 291, "index": "a7O", "elbowed": false}, {"id": "server-third-party-arrow", "type": "arrow", "x": 553.5550911482767, "y": 259.98981495367747, "width": 97.62868277146777, "height": 0.39559100553827875, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 25, "versionNonce": 1549053802, "isDeleted": false, "boundElements": [], "updated": 1751646783422, "link": null, "locked": false, "points": [[0, 0], [97.62868277146777, 0.39559100553827875]], "lastCommittedPoint": null, "startBinding": {"elementId": "server-box", "focus": -0.03295435338371885, "gap": 7.72137451171875}, "endBinding": {"elementId": "third-party-box", "focus": 0.033595647662262675, "gap": 9.2578125}, "startArrowhead": null, "endArrowhead": "arrow", "version": 84, "index": "a7V"}, {"id": "third-party-container-arrow", "type": "arrow", "x": 648.2566308764233, "y": 312.0366435033526, "width": 379.79777361238473, "height": 258.396870701903, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 26, "versionNonce": 2065687786, "isDeleted": false, "boundElements": [], "updated": 1751646672046, "link": null, "locked": false, "points": [[0, 0], [-379.79777361238473, 258.396870701903]], "lastCommittedPoint": null, "startBinding": {"elementId": "third-party-box", "focus": 0.15827082262455267, "gap": 13.719722594738693}, "endBinding": {"elementId": "docker-container-box", "focus": -0.15729065084848254, "gap": 9.791242841561836}, "startArrowhead": null, "endArrowhead": "arrow", "version": 168, "index": "a7d"}, {"id": "third-party-project-db-arrow", "type": "arrow", "x": 666.9368286132812, "y": 330, "width": 191.93682861328125, "height": 250, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 27, "versionNonce": 410206698, "isDeleted": false, "boundElements": [], "updated": 1751646611133, "link": null, "locked": false, "points": [[0, 0], [-191.93682861328125, 250]], "lastCommittedPoint": null, "startBinding": {"elementId": "third-party-box", "focus": 0.222494925526482, "gap": 11.703042055964149}, "endBinding": {"elementId": "project-db-box", "focus": -0.2905110565260376, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "version": 66, "index": "a7l"}, {"id": "third-party-redis-arrow", "type": "arrow", "x": 703.8932495117188, "y": 336.1751403808594, "width": 29.404296875, "height": 244.15689086914062, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 28, "versionNonce": 718544170, "isDeleted": false, "boundElements": [], "updated": 1751646615052, "link": null, "locked": false, "points": [[0, 0], [-29.404296875, 244.15689086914062]], "lastCommittedPoint": null, "startBinding": {"elementId": "third-party-box", "focus": 0.37078954239667716, "gap": 13.339813232421875}, "endBinding": {"elementId": "redis-box", "focus": -0.06625556891294763, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "version": 69, "index": "a7t"}, {"id": "title", "type": "text", "x": 350, "y": 10, "width": 300, "height": 30, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 20, "versionNonce": 20, "isDeleted": false, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 18, "fontFamily": 1, "text": "Web IDE 平台系统架构", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "Web IDE 平台系统架构", "lineHeight": 1.25, "baseline": 24, "index": "a8", "autoResize": true, "version": 1}, {"id": "client-server-label", "type": "text", "x": 204.12109375, "y": 229.35223388671875, "width": 117.45985412597656, "height": 12.5, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 21, "versionNonce": 1968132278, "isDeleted": false, "boundElements": [{"id": "client-server-arrow", "type": "arrow"}], "updated": 1751646580395, "link": null, "locked": false, "fontSize": 10, "fontFamily": 1, "text": "HTTP API / WebSocket", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "HTTP API / WebSocket", "lineHeight": 1.25, "baseline": 9, "index": "a9", "autoResize": true, "version": 119}, {"id": "websocket-label", "type": "text", "x": 256.8294677734375, "y": 315.9798278808594, "width": 80, "height": 15, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 22, "versionNonce": 1224494454, "isDeleted": false, "boundElements": [], "updated": 1751646649551, "link": null, "locked": false, "fontSize": 10, "fontFamily": 1, "text": "WebSocket中继", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "WebSocket中继", "lineHeight": 1.25, "baseline": 9, "index": "aA", "autoResize": true, "version": 181}, {"id": "container-comm-label", "type": "text", "x": 182.42840576171875, "y": 528.2877197265625, "width": 80, "height": 15, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 23, "versionNonce": 1309226742, "isDeleted": false, "boundElements": [], "updated": 1751646818790, "link": null, "locked": false, "fontSize": 10, "fontFamily": 1, "text": "内部通信协调", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "内部通信协调", "lineHeight": 1.25, "baseline": 9, "index": "aB", "autoResize": true, "version": 57}, {"id": "db-access-label", "type": "text", "x": 446.43878173828125, "y": 159.61260986328125, "width": 80, "height": 15, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 29, "versionNonce": 1611357418, "isDeleted": false, "boundElements": [{"id": "server-db-arrow", "type": "arrow"}], "updated": 1751646783911, "link": null, "locked": false, "fontSize": 10, "fontFamily": 1, "text": "仅服务端访问", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "仅服务端访问", "lineHeight": 1.25, "baseline": 9, "index": "aC", "autoResize": true, "version": 27}, {"id": "api-label", "type": "text", "x": 580, "y": 240, "width": 40, "height": 15, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 30, "versionNonce": 1174385706, "isDeleted": false, "boundElements": [], "updated": 1751646511284, "link": null, "locked": false, "fontSize": 10, "fontFamily": 1, "text": "API", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "API", "lineHeight": 1.25, "baseline": 9, "index": "aD", "autoResize": true, "version": 2}, {"id": "create-infra-label", "type": "text", "x": 450, "y": 450, "width": 100, "height": 15, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 31, "versionNonce": 1646558070, "isDeleted": false, "boundElements": [], "updated": 1751646511284, "link": null, "locked": false, "fontSize": 10, "fontFamily": 1, "text": "创建容器及基础设施", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "创建容器及基础设施", "lineHeight": 1.25, "baseline": 9, "index": "aE", "autoResize": true, "version": 2}], "appState": {"gridSize": 20, "gridStep": 5, "gridModeEnabled": false, "viewBackgroundColor": "#ffffff"}, "files": {}}