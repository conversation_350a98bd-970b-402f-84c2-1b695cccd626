# Web IDE 平台技术架构设计

## 系统架构概览

### 整体架构图

请查看 [架构图](./architecture.excalidraw) - 使用 Excalidraw 插件打开查看完整的系统架构图。

### 数据流说明

1. 用户在客户端创建项目请求
2. 服务端验证用户权限，在主PostgreSQL中创建项目记录
3. 服务端调用第三方Docker API创建完整的项目环境：
   - 创建Docker容器
   - 创建项目专用数据库（PostgreSQL）
   - 创建Redis缓存实例
   - 创建对象存储空间
4. 容器启动后，内部WebSocket客户端连接到服务端
5. 客户端通过服务端WebSocket中继与容器内WebSocket客户端通信
6. 用户操作（文件管理、AI对话）通过服务端转发到容器内WebSocket客户端
7. 容器内WebSocket客户端将请求转发给内部AI服务和文件管理系统
8. 容器内AI服务生成代码，文件系统执行增删改查操作
9. 处理结果通过WebSocket客户端返回给服务端，再转发给客户端
10. 服务端记录重要操作日志到主PostgreSQL
11. 容器内的预设框架可以使用项目专用的基础设施
12. 预览和测试在容器内进行，使用NestJS等预设框架
13. 部署时，服务端协调容器打包并调用第三方API部署

**数据访问权限**：

- **客户端**：只能通过服务端API访问数据
- **服务端**：拥有主PostgreSQL完全访问权限，负责平台数据管理
- **容器WebSocket客户端**：通过WebSocket与服务端通信，转发请求到内部服务
- **容器内AI服务**：封装Claude Code CLI，处理代码生成请求
- **第三方服务**：负责创建和管理项目的基础设施（数据库、缓存、存储等）

### WebSocket通信流程

```
客户端                    服务端                    容器服务
  │                        │                        │
  │──── file:read ────────►│                        │
  │                        │──── file:read ────────►│
  │                        │                        │
  │                        │◄─── file:content ─────│
  │◄─── file:content ─────│                        │
  │                        │                        │
  │──── ai:chat ──────────►│                        │
  │                        │──── ai:chat ──────────►│
  │                        │                        │
  │                        │◄─── ai:response ──────│
  │◄─── ai:response ──────│                        │
  │                        │                        │
  │                        │──── 记录到数据库 ─────►│ PostgreSQL
```

**服务端中继职责**：

1. 验证客户端请求权限
2. 转发合法请求到对应容器
3. 记录重要操作到主数据库
4. 处理容器连接管理
5. 提供负载均衡和故障转移

**第三方服务职责**：

1. 为每个项目创建独立的Docker容器
2. 为每个项目创建专用数据库实例（PostgreSQL）
3. 为每个项目创建Redis缓存实例
4. 为每个项目创建对象存储空间
5. 管理项目的完整基础设施生命周期
6. 提供镜像构建和部署服务

**架构优势**：

- **资源隔离**：每个项目拥有独立的基础设施
- **扩展性**：可以根据项目需求动态创建资源
- **安全性**：项目间完全隔离，避免数据泄露
- **灵活性**：支持不同类型的数据库和存储需求

### 容器内部架构详解

**重要原则**：Docker容器内部只有WebSocket客户端与服务端通信，其他服务都是内部服务。

#### WebSocket客户端服务（唯一对外通信）

```typescript
// WebSocket客户端服务 - 容器的唯一对外接口
class ContainerWebSocketClient {
  private socket: WebSocket
  private aiService: ClaudeCodeService
  private fileManager: FileManagerService
  private nestjsFramework: NestJSFramework

  // 连接到服务端（唯一对外连接）
  connect(serverUrl: string): void

  // 处理来自服务端的请求（唯一入口）
  handleMessage(message: WebSocketMessage): void

  // 转发请求到内部服务（内部调用）
  private forwardToAI(request: AIRequest): Promise<AIResponse>
  private forwardToFileManager(request: FileRequest): Promise<FileResponse>
  private forwardToFramework(request: FrameworkRequest): Promise<FrameworkResponse>
}
```

#### Claude Code CLI封装服务（内部服务）

```typescript
// Claude Code CLI封装服务 - 仅供容器内部调用
class ClaudeCodeService {
  private cliPath: string = '/usr/local/bin/claude-code'
  private requestQueue: Queue<AIRequest>

  // 注意：此服务不对外暴露，只能通过WebSocket客户端调用

  // 执行Claude Code CLI命令
  async generateCode(prompt: string, context: string): Promise<string> {
    const command = `${this.cliPath} --prompt "${prompt}" --context "${context}"`
    return await this.executeCommand(command)
  }

  // 执行CLI命令
  private async executeCommand(command: string): Promise<string> {
    return new Promise((resolve, reject) => {
      exec(command, (error, stdout, stderr) => {
        if (error) reject(error)
        else resolve(stdout)
      })
    })
  }

  // 队列管理，避免并发冲突
  async processRequest(request: AIRequest): Promise<AIResponse> {
    return await this.requestQueue.add(() =>
      this.generateCode(request.prompt, request.context)
    )
  }

  // 内部方法：连接项目数据库
  private async connectToProjectDB(): Promise<void> {
    // 连接到项目专用数据库
  }
}
```

#### 文件管理系统（内部服务）

```typescript
// 文件管理系统 - 仅供容器内部调用
class FileManagerService {
  private projectRoot: string
  private watcher: FSWatcher

  // 注意：此服务不对外暴露，只能通过WebSocket客户端调用

  // 文件操作
  async readFile(path: string): Promise<string>
  async writeFile(path: string, content: string): Promise<void>
  async createFile(path: string, type: 'file' | 'directory'): Promise<void>
  async deleteFile(path: string): Promise<void>
  async renameFile(oldPath: string, newPath: string): Promise<void>

  // 文件监听
  watchFiles(callback: (event: FileEvent) => void): void

  // 获取文件树
  async getFileTree(): Promise<FileNode[]>

  // 内部方法：与AI服务协作
  private async applyAIGeneratedCode(code: string, targetPath: string): Promise<void>
}
```

## 技术栈详细说明

### 前端技术栈

```typescript
// 核心框架
Next.js 15.2+ (React框架)
React 18+
TypeScript 5.0+

// UI组件库
shadcn/ui (基于Radix UI)
Tailwind CSS (样式框架)
Lucide React (图标库)
Monaco Editor (代码编辑器)

// 状态管理
Zustand / React Context

// 表单处理
React Hook Form
Zod (数据验证)

// HTTP客户端
Fetch API / Axios

// WebSocket
原生WebSocket / Socket.io-client

// 样式
Tailwind CSS
CSS Modules
```

### 后端技术栈

```typescript
// 核心框架
Bun.js 1.0+
Elysia 0.8+ (Web框架)
TypeScript 5.0+

// 数据库
PostgreSQL 15+
Prisma 5.0+ (ORM)
Redis 7.0+ (缓存)

// 认证授权
JWT (JSON Web Tokens)
bcrypt (密码加密)

// WebSocket
Elysia WebSocket插件

// 容器管理
Docker API客户端
```

### 容器服务技术栈

```typescript
// 基础环境
Node.js 20+ / Bun.js
Docker容器

// WebSocket客户端服务
Socket.io-client / ws
Express.js (内部API服务)

// AI代码生成服务
Claude Code CLI (封装为WebSocket服务)
child_process (执行CLI命令)
队列管理 (处理AI请求)

// 预设框架
NestJS 10+ (后端框架模板)
Vue 3 / React 18 (前端框架模板)

// 文件系统
Node.js fs模块
chokidar (文件监听)
path (路径处理)

// 项目基础设施
PostgreSQL/MySQL (项目数据库)
Redis (缓存服务)
MinIO/S3 (对象存储)
```

## 数据库设计

### Prisma Schema

```prisma
// schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  username  String   @unique
  email     String   @unique
  password  String
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  projects Project[]
  conversations AiConversation[]
  
  @@map("users")
}

model Project {
  id          String   @id @default(cuid())
  name        String
  description String?
  userId      String
  containerId String?  @unique
  status      ProjectStatus @default(CREATING)
  config      Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  user        User @relation(fields: [userId], references: [id], onDelete: Cascade)
  container   Container?
  conversations AiConversation[]
  deployments Deployment[]
  
  @@map("projects")
}

model Container {
  id        String   @id @default(cuid())
  projectId String   @unique
  dockerId  String   @unique
  status    ContainerStatus @default(CREATING)
  config    Json
  ports     Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  
  @@map("containers")
}

model AiConversation {
  id           String   @id @default(cuid())
  projectId    String
  userId       String
  userMessage  String
  aiResponse   String
  generatedCode String?
  applied      Boolean  @default(false)
  createdAt    DateTime @default(now())
  
  project      Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user         User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("ai_conversations")
}

model Deployment {
  id        String   @id @default(cuid())
  projectId String
  version   String
  status    DeploymentStatus @default(PENDING)
  imageTag  String
  config    Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  
  @@map("deployments")
}

enum ProjectStatus {
  CREATING
  ACTIVE
  STOPPED
  ERROR
  DELETED
}

enum ContainerStatus {
  CREATING
  RUNNING
  STOPPED
  ERROR
  DELETED
}

enum DeploymentStatus {
  PENDING
  BUILDING
  DEPLOYING
  DEPLOYED
  FAILED
}
```

## API设计

### RESTful API端点

```typescript
// 用户认证
POST   /api/users/register
POST   /api/users/login
POST   /api/users/logout
GET    /api/users/profile
PUT    /api/users/profile

// 应用管理 (AI开发项目)
GET    /api/applications
POST   /api/applications
GET    /api/applications/:id
PUT    /api/applications/:id
DELETE /api/applications/:id

// 容器管理
POST   /api/applications/:id/container/start
POST   /api/applications/:id/container/stop
POST   /api/applications/:id/container/restart

// AI对话
GET    /api/conversations/application/:id
POST   /api/conversations
GET    /api/conversations/application/:id/recent
GET    /api/conversations/application/:id/stats

// 管理员功能
GET    /api/admin/users
POST   /api/admin/users
PUT    /api/admin/users/:id
DELETE /api/admin/users/:id

// 系统配置
GET    /api/system-config
PUT    /api/system-config/:key

// 文件存储
GET    /api/s3-files
POST   /api/s3-files/upload
DELETE /api/s3-files/:id
```

### WebSocket事件

```typescript
// 客户端 -> 服务端
interface ClientEvents {
  'file:read': { path: string }
  'file:write': { path: string, content: string }
  'file:create': { path: string, type: 'file' | 'directory' }
  'file:delete': { path: string }
  'file:rename': { oldPath: string, newPath: string }
  
  'ai:chat': { message: string, context?: string }
  'ai:apply': { conversationId: string }
  
  'preview:start': { port?: number }
  'preview:stop': {}
  
  'terminal:command': { command: string }
}

// 服务端 -> 客户端
interface ServerEvents {
  'file:content': { path: string, content: string }
  'file:tree': { tree: FileNode[] }
  'file:changed': { path: string, type: 'created' | 'modified' | 'deleted' }
  
  'ai:response': { message: string, code?: string }
  'ai:error': { error: string }
  
  'preview:ready': { url: string }
  'preview:error': { error: string }
  
  'terminal:output': { output: string }
  'terminal:error': { error: string }
  
  'container:status': { status: ContainerStatus }
}
```

## 容器服务架构

### 容器内服务结构

```
/app
├── src/
│   ├── websocket/          # WebSocket服务
│   ├── ai/                 # AI代码生成服务
│   ├── file-manager/       # 文件管理服务
│   ├── preview/            # 预览服务
│   └── terminal/           # 终端服务
├── templates/              # 项目模板
│   ├── nestjs-api/
│   ├── vue-spa/
│   └── fullstack/
├── user-projects/          # 用户项目目录
└── package.json
```

### 容器服务主要模块

```typescript
// WebSocket服务
class WebSocketService {
  private io: Server
  
  handleConnection(socket: Socket) {
    socket.on('file:read', this.handleFileRead)
    socket.on('file:write', this.handleFileWrite)
    socket.on('ai:chat', this.handleAiChat)
    // ... 其他事件处理
  }
}

// AI代码生成服务
class AiCodeService {
  async generateCode(prompt: string, context: string): Promise<string> {
    // 调用AI API生成代码
  }
  
  async applyCode(code: string, targetPath: string): Promise<void> {
    // 将生成的代码应用到项目中
  }
}

// 文件管理服务
class FileManagerService {
  async readFile(path: string): Promise<string>
  async writeFile(path: string, content: string): Promise<void>
  async createFile(path: string, type: 'file' | 'directory'): Promise<void>
  async deleteFile(path: string): Promise<void>
  async getFileTree(rootPath: string): Promise<FileNode[]>
}
```

## 部署架构

### 开发环境

```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: webide_dev
      POSTGRES_USER: dev
      POSTGRES_PASSWORD: dev123
    ports:
      - "5432:5432"
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
  
  api:
    build: ./server
    ports:
      - "3000:3000"
    depends_on:
      - postgres
      - redis
    environment:
      DATABASE_URL: *************************************/webide_dev
      REDIS_URL: redis://redis:6379
  
  web:
    build: ./web
    ports:
      - "5173:5173"
    depends_on:
      - api
```

### 生产环境

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
  
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
  
  api:
    image: webide-api:latest
    environment:
      NODE_ENV: production
      DATABASE_URL: ${DATABASE_URL}
      REDIS_URL: ${REDIS_URL}
      JWT_SECRET: ${JWT_SECRET}
    depends_on:
      - postgres
      - redis
  
  web:
    image: webide-web:latest
    environment:
      NODE_ENV: production
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
      - web

volumes:
  postgres_data:
  redis_data:
```

## 安全考虑

### 认证授权

- JWT Token认证
- 刷新Token机制
- 权限基于角色控制(RBAC)
- API接口权限验证

### 容器安全

- 容器间网络隔离
- 资源限制配置
- 文件系统权限控制
- 敏感信息环境变量管理

### 数据安全

- 数据库连接加密
- 敏感数据加密存储
- API接口HTTPS
- 输入数据验证和清理

## 性能优化

### 前端优化

- 代码分割和懒加载
- 静态资源CDN
- 组件缓存
- 虚拟滚动

### 后端优化

- 数据库查询优化
- Redis缓存策略
- API响应压缩
- 连接池管理

### 容器优化

- 镜像分层优化
- 资源使用监控
- 自动扩缩容
- 负载均衡

## 监控和日志

### 应用监控

- 性能指标收集
- 错误日志记录
- 用户行为分析
- 系统健康检查

### 基础设施监控

- 服务器资源监控
- 数据库性能监控
- 容器状态监控
- 网络流量监控
