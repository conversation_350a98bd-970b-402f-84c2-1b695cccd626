import type {
  ApiResponse,
  LoginCredentials,
  LoginResponse,
  RegisterData,
  RegisterResponse,
  User,
  Project,
  CreateProjectData,
  Application,
  CreateApplicationData,
  ApplicationListResponse,
  PaginatedResponse,
  FrameworkStatus,
  FrameworkListResponse
} from '../types';

// API配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001';
const API_PREFIX = '/api';

// 请求配置接口
interface RequestConfig extends RequestInit {
  requireAuth?: boolean;
  timeout?: number;
}

// API错误类
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public response?: Response
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// HTTP客户端类
class HttpClient {
  private baseURL: string;
  private defaultTimeout: number = 10000;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  // 获取认证token
  private getAuthToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('auth_token');
  }

  // 设置认证token
  setToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  // 清除认证token
  private clearAuthToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
    }
  }

  // 构建完整URL
  private buildURL(endpoint: string): string {
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `${this.baseURL}${API_PREFIX}${cleanEndpoint}`;
  }

  // 构建请求头
  private buildHeaders(config: RequestConfig = {}): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // 添加认证头
    if (config.requireAuth !== false) {
      const token = this.getAuthToken();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    }

    return headers;
  }

  // 处理响应
  private async handleResponse<T>(response: Response): Promise<T> {
    // 检查响应状态
    if (!response.ok) {
      let errorMessage = `HTTP Error: ${response.status}`;
      let errorData: { error?: string; message?: string; code?: string } | null = null;

      try {
        errorData = await response.json();
        errorMessage = errorData?.error || errorData?.message || errorMessage;
      } catch {
        // 如果无法解析错误响应，使用默认错误消息
      }

      // 处理认证错误
      if (response.status === 401) {
        this.clearAuthToken();
        // 可以在这里触发登录页面跳转
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      }

      throw new ApiError(
        errorMessage,
        response.status,
        errorData?.code,
        response
      );
    }

    // 解析响应数据
    try {
      return await response.json();
    } catch (error) {
      throw new ApiError('Failed to parse response JSON', response.status);
    }
  }

  // 通用请求方法
  async request<T>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<T> {
    const url = this.buildURL(endpoint);
    const headers = this.buildHeaders(config);
    const timeout = config.timeout || this.defaultTimeout;

    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...config,
        headers: {
          ...headers,
          ...config.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return await this.handleResponse<T>(response);
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new ApiError('Request timeout');
        }
        throw new ApiError(error.message);
      }
      
      throw new ApiError('Unknown error occurred');
    }
  }

  // GET请求
  async get<T>(endpoint: string, config?: RequestConfig): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'GET' });
  }

  // POST请求
  async post<T>(
    endpoint: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // PUT请求
  async put<T>(
    endpoint: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // DELETE请求
  async delete<T>(endpoint: string, config?: RequestConfig): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' });
  }

  // PATCH请求
  async patch<T>(
    endpoint: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
}

// 创建HTTP客户端实例
export const httpClient = new HttpClient(API_BASE_URL);

// ===== 认证API =====
export const authApi = {
  // 登录
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const response = await httpClient.post<LoginResponse>('/users/login', credentials, {
      requireAuth: false,
    });
    
    // 登录成功后设置token
    if (response.token) {
      httpClient.setToken(response.token);
    }
    
    return response;
  },

  // 注册
  async register(data: RegisterData): Promise<RegisterResponse> {
    return httpClient.post<RegisterResponse>('/users/register', data, {
      requireAuth: false,
    });
  },

  // Google登录
  async loginWithGoogle(credential: string): Promise<LoginResponse> {
    const response = await httpClient.post<LoginResponse>('/users/google-login', {
      credential,
    }, {
      requireAuth: false,
    });
    
    // 登录成功后设置token
    if (response.token) {
      httpClient.setToken(response.token);
    }
    
    return response;
  },

  // 登出
  logout(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
    }
  },

  // 刷新token
  async refreshToken(): Promise<{ token: string }> {
    return httpClient.post<{ token: string }>('/users/refresh-token');
  },

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    return httpClient.get<User>('/users/me');
  },
};

// ===== 框架API =====
export const frameworkApi = {
  // 获取框架列表
  async getFrameworks(): Promise<FrameworkListResponse> {
    return httpClient.get<FrameworkListResponse>('/frameworks', {
      requireAuth: false,
    });
  },
};

// ===== 项目API =====
export const projectApi = {
  // 获取项目列表
  async getProjects(): Promise<Project[]> {
    const response = await httpClient.get<ApiResponse<Project[]>>('/projects');
    return response.data || [];
  },

  // 获取单个项目
  async getProject(id: string): Promise<Project> {
    const response = await httpClient.get<ApiResponse<Project>>(`/projects/${id}`);
    if (!response.data) {
      throw new ApiError('Project not found');
    }
    return response.data;
  },

  // 创建项目
  async createProject(data: CreateProjectData): Promise<Project> {
    const response = await httpClient.post<ApiResponse<Project>>('/projects', data);
    if (!response.data) {
      throw new ApiError('Failed to create project');
    }
    return response.data;
  },

  // 更新项目
  async updateProject(id: string, data: Partial<Project>): Promise<Project> {
    const response = await httpClient.put<ApiResponse<Project>>(`/projects/${id}`, data);
    if (!response.data) {
      throw new ApiError('Failed to update project');
    }
    return response.data;
  },

  // 删除项目
  async deleteProject(id: string): Promise<void> {
    await httpClient.delete(`/projects/${id}`);
  },
};

// ===== 应用API (对应server端的applications接口) =====
export const applicationApi = {
  // 获取用户的应用列表
  async getApplications(params?: { status?: string; page?: number; pageSize?: number }): Promise<ApplicationListResponse> {
    const queryParams = new URLSearchParams();
    if (params?.status) queryParams.append('status', params.status);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.pageSize) queryParams.append('pageSize', params.pageSize.toString());

    const queryString = queryParams.toString();
    const url = queryString ? `/applications?${queryString}` : '/applications';

    const response = await httpClient.get<ApplicationListResponse>(url);
    return response;
  },

  // 获取单个应用
  async getApplication(id: string): Promise<Application> {
    const response = await httpClient.get<ApiResponse<Application>>(`/applications/${id}`);
    if (!response.data) {
      throw new ApiError('Application not found');
    }
    return response.data;
  },

  // 创建应用
  async createApplication(data: CreateApplicationData): Promise<Application> {
    const response = await httpClient.post<ApiResponse<Application>>('/applications', data);
    if (!response.data) {
      throw new ApiError('Failed to create application');
    }
    return response.data;
  },

  // 更新应用
  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {
    const response = await httpClient.put<ApiResponse<Application>>(`/applications/${id}`, data);
    if (!response.data) {
      throw new ApiError('Failed to update application');
    }
    return response.data;
  },

  // 删除应用
  async deleteApplication(id: string): Promise<void> {
    await httpClient.delete(`/applications/${id}`);
  },
};
