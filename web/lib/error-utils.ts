// ===== 错误处理工具 =====

export const errorUtils = {
  // 获取错误消息
  getMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    return '未知错误';
  },

  // 检查是否为网络错误
  isNetworkError(error: unknown): boolean {
    if (error instanceof Error) {
      return error.message.includes('fetch') || 
             error.message.includes('network') ||
             error.message.includes('timeout');
    }
    return false;
  },
};
