import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// 重新导出所有工具函数
export { storage } from './storage'
export { dateUtils } from './date-utils'
export { validators } from './validators'
export { stringUtils } from './string-utils'
export { arrayUtils } from './array-utils'
export { objectUtils } from './object-utils'
export { errorUtils } from './error-utils'
export { debounce, throttle } from './debounce'
