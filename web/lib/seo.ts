import { Metadata } from 'next'

// SEO配置接口
export interface SEOConfig {
  title: string;
  description: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'blog';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
}

// 默认SEO配置
export const defaultSEO: SEOConfig = {
  title: 'XCoding - AI驱动的开发平台',
  description: '使用AI技术加速您的开发流程，提供智能代码生成、项目管理和协作工具。',
  keywords: ['AI开发', '代码生成', '项目管理', '开发工具', 'XCoding'],
  image: '/images/og-image.jpg',
  type: 'website',
  author: 'XCoding Team'
};

// 生成 Next.js Metadata
export function generateMetadata(config: Partial<SEOConfig> = {}): Metadata {
  const seo = { ...defaultSEO, ...config };
  const fullTitle = config.title ? `${config.title} | XCoding` : seo.title;

  return {
    title: fullTitle,
    description: seo.description,
    keywords: seo.keywords,
    authors: seo.author ? [{ name: seo.author }] : undefined,
    openGraph: {
      title: fullTitle,
      description: seo.description,
      type: seo.type === 'article' ? 'article' : 'website',
      images: seo.image ? [{ url: seo.image }] : undefined,
      url: seo.url,
      publishedTime: seo.publishedTime,
      modifiedTime: seo.modifiedTime,
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description: seo.description,
      images: seo.image ? [seo.image] : undefined,
    },
    alternates: {
      canonical: seo.url,
    },
  };
}

// 博客文章SEO
interface BlogPostSEOProps {
  title: string;
  description: string;
  author: string;
  publishedAt: string;
  updatedAt?: string;
  slug: string;
  tags?: string[];
}

export function generateBlogPostMetadata({
  title,
  description,
  author,
  publishedAt,
  updatedAt,
  slug,
  tags = []
}: BlogPostSEOProps): Metadata {
  return generateMetadata({
    title,
    description,
    type: 'article',
    author,
    publishedTime: publishedAt,
    modifiedTime: updatedAt,
    url: `https://xcoding.com/blog/${slug}`,
    keywords: [...(defaultSEO.keywords || []), ...tags],
  });
}
