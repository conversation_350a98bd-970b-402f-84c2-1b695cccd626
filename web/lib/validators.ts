// ===== 验证工具 =====

export const validators = {
  // 邮箱验证
  email(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // 密码强度验证
  password(password: string): { valid: boolean; message: string } {
    if (password.length < 8) {
      return { valid: false, message: '密码至少需要8位字符' };
    }
    if (!/[a-z]/.test(password)) {
      return { valid: false, message: '密码需要包含小写字母' };
    }
    if (!/[A-Z]/.test(password)) {
      return { valid: false, message: '密码需要包含大写字母' };
    }
    if (!/\d/.test(password)) {
      return { valid: false, message: '密码需要包含数字' };
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      return { valid: false, message: '密码需要包含特殊字符' };
    }
    return { valid: true, message: '密码强度良好' };
  },

  // 用户名验证
  username(username: string): { valid: boolean; message: string } {
    if (username.length < 3) {
      return { valid: false, message: '用户名至少需要3位字符' };
    }
    if (username.length > 20) {
      return { valid: false, message: '用户名不能超过20位字符' };
    }
    if (!/^[a-zA-Z0-9_]+$/.test(username)) {
      return { valid: false, message: '用户名只能包含字母、数字和下划线' };
    }
    return { valid: true, message: '用户名格式正确' };
  },

  // 项目名称验证
  projectName(name: string): { valid: boolean; message: string } {
    if (name.length < 1) {
      return { valid: false, message: '项目名称不能为空' };
    }
    if (name.length > 50) {
      return { valid: false, message: '项目名称不能超过50个字符' };
    }
    return { valid: true, message: '项目名称格式正确' };
  },
};
