// ===== 本地存储工具 =====

export const storage = {
  // 获取存储项
  get<T>(key: string, defaultValue?: T): T | null {
    try {
      // 检查是否在浏览器环境
      if (typeof window === 'undefined') {
        return defaultValue || null;
      }

      const item = localStorage.getItem(key);
      if (item === null) {
        return defaultValue || null;
      }

      // 尝试解析JSON，如果失败则返回原始字符串
      try {
        return JSON.parse(item);
      } catch {
        // 如果不是有效的JSON，直接返回字符串
        return item as T;
      }
    } catch (error) {
      console.warn(`Failed to get item from localStorage: ${key}`, error);
      return defaultValue || null;
    }
  },

  // 设置存储项
  set<T>(key: string, value: T): boolean {
    try {
      // 检查是否在浏览器环境
      if (typeof window === 'undefined') {
        return false;
      }

      // 如果是字符串，直接存储；否则JSON序列化
      const serialized = typeof value === 'string' ? value : JSON.stringify(value);
      localStorage.setItem(key, serialized);
      return true;
    } catch (error) {
      console.warn(`Failed to set item to localStorage: ${key}`, error);
      return false;
    }
  },

  // 删除存储项
  remove(key: string): boolean {
    try {
      // 检查是否在浏览器环境
      if (typeof window === 'undefined') {
        return false;
      }

      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.warn(`Failed to remove item from localStorage: ${key}`, error);
      return false;
    }
  },

  // 清空所有存储
  clear(): boolean {
    try {
      // 检查是否在浏览器环境
      if (typeof window === 'undefined') {
        return false;
      }

      localStorage.clear();
      return true;
    } catch (error) {
      console.warn('Failed to clear localStorage', error);
      return false;
    }
  },

  // 检查键是否存在
  has(key: string): boolean {
    // 检查是否在浏览器环境
    if (typeof window === 'undefined') {
      return false;
    }

    return localStorage.getItem(key) !== null;
  },
};
