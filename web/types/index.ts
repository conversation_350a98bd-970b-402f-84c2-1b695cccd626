// ===== 用户相关类型 =====

export interface User {
  id: string;
  username: string;
  email?: string;
  name: string;
  role: UserRole;
  avatar?: string;
  createdAt: string;
  updatedAt?: string;
}

export type UserRole = 'USER' | 'ADMIN';

export interface AuthUser {
  id: string;
  username: string;
  role: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
}

export interface RegisterResponse {
  message: string;
  user: Omit<User, 'password'>;
}

// ===== 框架相关类型 =====

export type FrameworkStatus = 'ENABLED' | 'COMING_SOON' | 'DISABLED';

export interface Framework {
  id: string;
  name: string;
  description?: string;
  status: FrameworkStatus;
  introduction?: string;
  sortOrder: number;
}

export interface FrameworkListResponse {
  success: boolean;
  data: Framework[];
}

// ===== 项目相关类型 =====

export interface Project {
  id: string;
  name: string;
  description: string;
  template: string;
  status: ProjectStatus;
  createdAt: string;
  updatedAt?: string;
  tags?: string[];
  userId?: string;
}

export type ProjectStatus = 'idle' | 'running' | 'building' | 'error' | 'stopped';

export interface CreateProjectData {
  name: string;
  description: string;
  template: string;
  tags?: string[];
}

// ===== 应用相关类型 (对应server端的Application模型) =====

export interface Application {
  id: string;
  name: string;
  description?: string;
  userId: string;
  frameworkId?: string;
  framework?: {
    id: string;
    name: string;
    description?: string;
    status: FrameworkStatus;
  };
  status: ApplicationStatus;
  config?: any;
  createdAt: string;
  updatedAt: string;
}

export type ApplicationStatus = 'CREATING' | 'ACTIVE' | 'PAUSED' | 'ERROR' | 'DELETED';

export interface CreateApplicationData {
  name: string;
  description?: string;
  frameworkId?: string;
  config?: any;
}

export interface ApplicationListResponse {
  success: boolean;
  data: Application[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// ===== 文件系统相关类型 =====

export interface FileNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  children?: FileNode[];
  content?: string;
  language?: string;
  path?: string;
}

// ===== 日志相关类型 =====

export interface LogEntry {
  id: string;
  timestamp: string;
  level: LogLevel;
  message: string;
  source: LogSource;
}

export type LogLevel = 'info' | 'warn' | 'error' | 'log' | 'debug';
export type LogSource = 'frontend' | 'backend' | 'system';

// ===== 数据库相关类型 =====

export interface DatabaseTable {
  name: string;
  rowCount: number;
  columns: DatabaseColumn[];
}

export interface DatabaseColumn {
  name: string;
  type: string;
  nullable: boolean;
  primaryKey?: boolean;
  foreignKey?: string;
}

export interface DatabaseRow {
  [key: string]: any;
}

// ===== API相关类型 =====

export interface ApiResponse<T = any> {
  success?: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// ===== 应用状态相关类型 =====

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

export interface AppState {
  currentProject: Project | null;
  projects: Project[];
  loading: boolean;
  error: string | null;
}

// ===== 表单相关类型 =====

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'textarea' | 'select';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
}

export interface FormErrors {
  [key: string]: string;
}

// ===== 工具类型 =====

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// ===== 常量类型 =====

export const PROJECT_TEMPLATES = {
  REACT_TS: 'react-ts',
  VUE_TS: 'vue-ts',
  NODE_EXPRESS: 'node-express',
  NEXT_JS: 'next-js',
  REACT_NATIVE: 'react-native',
} as const;

export type ProjectTemplate = typeof PROJECT_TEMPLATES[keyof typeof PROJECT_TEMPLATES];

export const USER_ROLES = {
  USER: 'USER',
  ADMIN: 'ADMIN',
} as const;

export const LOG_LEVELS = {
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
  LOG: 'log',
  DEBUG: 'debug',
} as const;

export const PROJECT_STATUSES = {
  IDLE: 'idle',
  RUNNING: 'running',
  BUILDING: 'building',
  ERROR: 'error',
  STOPPED: 'stopped',
} as const;

// ===== 博客和内容相关类型 =====

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  description: string;
  content: string;
  excerpt: string;
  author: {
    name: string;
    avatar?: string;
    bio?: string;
  };
  tags: string[];
  category: string;
  publishedAt: string;
  updatedAt?: string;
  readingTime: number; // 预计阅读时间（分钟）
  featured: boolean;
  published: boolean;
  seo: {
    title?: string;
    description?: string;
    keywords?: string[];
  };
}

export interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  postCount: number;
}

export interface BlogTag {
  id: string;
  name: string;
  slug: string;
  postCount: number;
}

export interface BlogListResponse {
  posts: BlogPost[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  categories: BlogCategory[];
  tags: BlogTag[];
}

// ===== 页面路由类型 =====

export type PageType = 'marketing' | 'app';

export interface RouteConfig {
  path: string;
  component: React.ComponentType;
  type: PageType;
  seo?: {
    title: string;
    description: string;
    keywords?: string[];
  };
  prerender?: boolean; // 是否预渲染
}
