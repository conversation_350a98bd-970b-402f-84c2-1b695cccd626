'use client'

import { useContext, useEffect } from 'react';
import { AuthContext, type AuthContextType } from '../contexts/auth-context';

// 使用认证上下文的Hook
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// 检查是否已认证的Hook
export const useRequireAuth = (): AuthContextType => {
  const auth = useAuth();

  useEffect(() => {
    if (!auth.loading && !auth.isAuthenticated) {
      // 可以在这里处理未认证的情况，比如跳转到登录页
      console.warn('User is not authenticated');
    }
  }, [auth.loading, auth.isAuthenticated]);

  return auth;
};
