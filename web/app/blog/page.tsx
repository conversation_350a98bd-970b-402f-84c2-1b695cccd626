'use client'

import React from 'react';
import Link from 'next/link';
import { ArrowLeft, Calendar, Clock, User } from 'lucide-react';

// 模拟博客数据
const mockBlogPosts = [
  {
    id: '1',
    title: 'Next.js 15 新特性详解',
    slug: 'nextjs-15-features',
    description: '探索 Next.js 15 带来的革命性变化，包括 App Router 的改进、性能优化和新的开发体验。',
    excerpt: '本文将深入介绍 Next.js 15 的主要新特性，包括改进的 App Router、更好的性能优化、新的缓存策略等...',
    author: {
      name: 'XCoding Team',
      avatar: '/placeholder-user.jpg',
    },
    publishedAt: '2024-01-15',
    readingTime: 8,
    category: '技术分享',
    tags: ['Next.js', 'React', 'Web开发'],
  },
  {
    id: '2',
    title: 'AI 辅助编程的未来',
    slug: 'future-of-ai-programming',
    description: '探讨 AI 如何改变软件开发的方式，以及开发者如何适应这个新时代。',
    excerpt: 'AI 正在重塑软件开发的方式。从代码生成到自动化测试，AI 工具正在成为开发者的得力助手...',
    author: {
      name: 'XCoding Team',
      avatar: '/placeholder-user.jpg',
    },
    publishedAt: '2024-01-12',
    readingTime: 6,
    category: 'AI技术',
    tags: ['AI', '编程', '未来趋势'],
  },
  {
    id: '3',
    title: '云端开发环境搭建指南',
    slug: 'cloud-development-setup',
    description: '详细介绍如何搭建高效的云端开发环境，提升开发效率。',
    excerpt: '云端开发环境可以让开发者随时随地进行编程工作。本文将介绍如何搭建一个完整的云端开发环境...',
    author: {
      name: 'XCoding Team',
      avatar: '/placeholder-user.jpg',
    },
    publishedAt: '2024-01-10',
    readingTime: 10,
    category: '开发工具',
    tags: ['云开发', 'DevOps', '工具'],
  },
];

export default function BlogPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-6 py-4">
          <Link 
            href="/" 
            className="inline-flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>返回首页</span>
          </Link>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="container mx-auto px-6 py-12">
        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">技术博客</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            分享最新的技术趋势、开发经验和行业洞察
          </p>
        </div>

        {/* 博客文章列表 */}
        <div className="max-w-4xl mx-auto">
          <div className="space-y-8">
            {mockBlogPosts.map((post) => (
              <article 
                key={post.id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 hover:shadow-md transition-shadow"
              >
                {/* 文章元信息 */}
                <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                    {post.category}
                  </span>
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>{post.publishedAt}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{post.readingTime} 分钟阅读</span>
                  </div>
                </div>

                {/* 文章标题和描述 */}
                <h2 className="text-2xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
                  <Link href={`/blog/${post.slug}`}>
                    {post.title}
                  </Link>
                </h2>
                
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {post.excerpt}
                </p>

                {/* 标签 */}
                <div className="flex items-center justify-between">
                  <div className="flex flex-wrap gap-2">
                    {post.tags.map((tag) => (
                      <span 
                        key={tag}
                        className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                  
                  {/* 作者信息 */}
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600">{post.author.name}</span>
                  </div>
                </div>
              </article>
            ))}
          </div>

          {/* 加载更多按钮 */}
          <div className="text-center mt-12">
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
              加载更多文章
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}
