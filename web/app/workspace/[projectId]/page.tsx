'use client'

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import dynamic from 'next/dynamic';
import WorkspaceHeader from '@/components/workspace-header';
import ProjectSidebar from '@/components/project-sidebar';
import MainWorkspace from '@/components/main-workspace';
import AIAssistantPanel from '@/components/ai-assistant-panel';
import DevDatabasePanel from '@/components/dev-database-panel';
import EditProjectModal from '@/components/edit-project-modal';
import UnifiedDeployModal from '@/components/unified-deploy-modal';
import ProductionManagementPanel from '@/components/production-management-panel';

// 动态导入ProtectedRoute，禁用SSR
const ProtectedRoute = dynamic(() => import('@/components/protected-route'), {
  ssr: false,
  loading: () => (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>
  )
});

// 接口定义
interface Project {
  id: string;
  name: string;
  description: string;
  template: string;
  status: 'idle' | 'running' | 'building' | 'error';
  createdAt: string;
  updatedAt?: string;
  tags?: string[];
}

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'log';
  message: string;
  source: 'frontend' | 'backend';
}

interface FileNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  children?: FileNode[];
  content?: string;
  language?: string;
}

function DevelopmentWorkspaceContent() {
  const params = useParams();
  const projectId = params.projectId as string;
  
  // 状态管理
  const [currentProject, setCurrentProject] = useState<Project | null>(null);
  const [projects, setProjects] = useState<Project[]>([]);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [activeTab, setActiveTab] = useState<'preview' | 'code'>('preview');
  const [consoleVisible, setConsoleVisible] = useState(false);
  const [frontendFilter, setFrontendFilter] = useState('all');
  const [backendFilter, setBackendFilter] = useState('all');
  const [selectedFile, setSelectedFile] = useState('app/page.tsx');
  const [fileTree, setFileTree] = useState<FileNode[]>([]);
  const [frontendLogs, setFrontendLogs] = useState<LogEntry[]>([]);
  const [backendLogs, setBackendLogs] = useState<LogEntry[]>([]);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['app', 'components']));
  const [aiPanelCollapsed, setAiPanelCollapsed] = useState(false);
  const [consoleHeight, setConsoleHeight] = useState(250);
  const [isResizingConsole, setIsResizingConsole] = useState(false);
  const [showDevDatabase, setShowDevDatabase] = useState(false);
  const [showEditProject, setShowEditProject] = useState(false);
  const [showUnifiedDeployModal, setShowUnifiedDeployModal] = useState(false);
  const [showProductionPanel, setShowProductionPanel] = useState(false);

  // 初始化数据
  useEffect(() => {
    // 加载项目数据
    const loadProjects = () => {
      try {
        const savedProjects = localStorage.getItem('xcoding_projects');
        if (savedProjects) {
          const parsedProjects = JSON.parse(savedProjects);
          setProjects(parsedProjects);
          
          const foundProject = parsedProjects.find((p: Project) => p.id === projectId);
          if (foundProject) {
            setCurrentProject(foundProject);
          }
        }
      } catch (error) {
        console.error('Error loading projects:', error);
      }
    };

    loadProjects();

    // 初始化文件树
    setFileTree([
      {
        id: 'app',
        name: 'app',
        type: 'folder',
        children: [
          { id: 'app/page.tsx', name: 'page.tsx', type: 'file', language: 'typescript' },
          { id: 'app/layout.tsx', name: 'layout.tsx', type: 'file', language: 'typescript' },
          { id: 'app/globals.css', name: 'globals.css', type: 'file', language: 'css' }
        ]
      },
      {
        id: 'components',
        name: 'components',
        type: 'folder',
        children: [
          { id: 'components/Header.tsx', name: 'Header.tsx', type: 'file', language: 'typescript' },
          { id: 'components/Footer.tsx', name: 'Footer.tsx', type: 'file', language: 'typescript' }
        ]
      },
      { id: 'package.json', name: 'package.json', type: 'file', language: 'json' },
      { id: 'README.md', name: 'README.md', type: 'file', language: 'markdown' }
    ]);

    // 模拟日志数据
    setFrontendLogs([
      {
        id: '1',
        timestamp: new Date().toISOString(),
        level: 'info',
        message: '应用启动成功',
        source: 'frontend'
      }
    ]);

    setBackendLogs([
      {
        id: '2',
        timestamp: new Date().toISOString(),
        level: 'info',
        message: 'API服务器启动在端口 3000',
        source: 'backend'
      }
    ]);
  }, [projectId]);

  // 项目操作函数
  const handleRunProject = () => {
    if (currentProject) {
      const newStatus: 'idle' | 'running' | 'building' | 'error' = currentProject.status === 'running' ? 'idle' : 'running';
      const updatedProject: Project = { ...currentProject, status: newStatus };
      setCurrentProject(updatedProject);
      
      // 更新localStorage
      try {
        const projects = JSON.parse(localStorage.getItem('xcoding_projects') || '[]');
        const updatedProjects = projects.map((p: Project) => 
          p.id === projectId ? updatedProject : p
        );
        localStorage.setItem('xcoding_projects', JSON.stringify(updatedProjects));
        setProjects(updatedProjects);
      } catch (error) {
        console.error('Error updating project:', error);
      }
    }
  };

  const handleSaveProject = () => {
    console.log('Project saved');
  };

  const handleShowDatabase = () => {
    setShowDevDatabase(true);
  };

  const handleEditProject = () => {
    setShowEditProject(true);
  };

  const handleToggleAIPanel = () => {
    setAiPanelCollapsed(!aiPanelCollapsed);
  };

  const handleProjectSelect = (project: Project) => {
    setCurrentProject(project);
    // 可以在这里添加路由跳转逻辑
  };

  const handleProjectUpdate = (updatedProject: Project) => {
    setCurrentProject(updatedProject);
    const updatedProjects = projects.map(p =>
      p.id === updatedProject.id ? updatedProject : p
    );
    setProjects(updatedProjects);
    
    // 保存到localStorage
    try {
      localStorage.setItem('xcoding_projects', JSON.stringify(updatedProjects));
    } catch (error) {
      console.error('Error saving projects:', error);
    }
  };

  const handleConsoleResize = (height: number) => {
    setConsoleHeight(height);
  };

  return (
    <div className="h-screen bg-slate-50 flex flex-col overflow-hidden">
      {/* 工作区头部 */}
      <WorkspaceHeader
        currentProject={currentProject}
        onRunProject={handleRunProject}
        onSaveProject={handleSaveProject}
        onShowDatabase={handleShowDatabase}
        onToggleAIPanel={handleToggleAIPanel}
        aiPanelCollapsed={aiPanelCollapsed}
        sidebarVisible={sidebarVisible}
        setSidebarVisible={setSidebarVisible}
        onProjectUpdate={handleProjectUpdate}
        onEditProject={handleEditProject}
        onShowUnifiedDeployModal={() => setShowUnifiedDeployModal(true)}
        onShowProductionPanel={() => setShowProductionPanel(true)}
      />

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 主工作区 */}
        <div className={`flex-1 transition-all duration-300 overflow-hidden ${aiPanelCollapsed ? 'mr-0' : 'mr-96'}`}>
          <MainWorkspace
            currentProject={currentProject}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            consoleVisible={consoleVisible}
            setConsoleVisible={setConsoleVisible}
            frontendFilter={frontendFilter}
            setFrontendFilter={setFrontendFilter}
            backendFilter={backendFilter}
            setBackendFilter={setBackendFilter}
            selectedFile={selectedFile}
            setSelectedFile={setSelectedFile}
            fileTree={fileTree}
            setFileTree={setFileTree}
            frontendLogs={frontendLogs}
            backendLogs={backendLogs}
            expandedFolders={expandedFolders}
            setExpandedFolders={setExpandedFolders}
            consoleHeight={consoleHeight}
            handleConsoleResize={handleConsoleResize}
            isResizingConsole={isResizingConsole}
            setIsResizingConsole={setIsResizingConsole}
          />
        </div>

        {/* AI助手面板 */}
        <div
          className={`fixed right-0 bg-white transition-transform duration-300 ${
            aiPanelCollapsed ? 'translate-x-full' : 'translate-x-0'
          }`}
          style={{
            width: '384px',
            top: '48px',
            height: 'calc(100vh - 48px)'
          }}
        >
          <AIAssistantPanel
            currentProject={currentProject}
            collapsed={aiPanelCollapsed}
          />
        </div>
      </div>

      {/* 项目侧边栏 */}
      <ProjectSidebar
        projects={projects}
        currentProject={currentProject}
        onProjectSelect={handleProjectSelect}
        onProjectUpdate={handleProjectUpdate}
        visible={sidebarVisible}
        onClose={() => setSidebarVisible(false)}
      />

      {/* 开发数据库面板 */}
      <DevDatabasePanel
        isOpen={showDevDatabase}
        onClose={() => setShowDevDatabase(false)}
      />

      {/* 编辑项目模态框 */}
      <EditProjectModal
        isOpen={showEditProject}
        onClose={() => setShowEditProject(false)}
        project={currentProject}
        onSave={handleProjectUpdate}
      />

      {/* 统一部署模态框 */}
      <UnifiedDeployModal
        isOpen={showUnifiedDeployModal}
        onClose={() => setShowUnifiedDeployModal(false)}
        project={currentProject}
      />

      {/* 生产环境管理面板 */}
      <ProductionManagementPanel
        isOpen={showProductionPanel}
        onClose={() => setShowProductionPanel(false)}
        project={currentProject}
      />
    </div>
  );
}

export default function DevelopmentWorkspacePage() {
  return (
    <ProtectedRoute>
      <DevelopmentWorkspaceContent />
    </ProtectedRoute>
  );
}
