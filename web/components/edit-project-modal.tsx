'use client'

import React, { useState, useEffect } from 'react';
import { X, Save, FileText, Tag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface Project {
  id: string;
  name: string;
  description: string;
  template: string;
  status: 'idle' | 'running' | 'building' | 'error';
  createdAt: string;
  updatedAt?: string;
  tags?: string[];
}

interface EditProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project | null;
  onSave: (project: Project) => void;
}

const EditProjectModal: React.FC<EditProjectModalProps> = ({
  isOpen,
  onClose,
  project,
  onSave
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    tags: ''
  });
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (project && isOpen) {
      setFormData({
        name: project.name,
        description: project.description,
        tags: project.tags?.join(', ') || ''
      });
    }
  }, [project, isOpen]);

  const handleSave = async () => {
    if (!project) return;
    
    setIsSaving(true);
    
    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const updatedProject: Project = {
      ...project,
      name: formData.name.trim(),
      description: formData.description.trim(),
      tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
      updatedAt: new Date().toISOString()
    };
    
    onSave(updatedProject);
    setIsSaving(false);
    onClose();
  };

  const handleCancel = () => {
    if (project) {
      setFormData({
        name: project.name,
        description: project.description,
        tags: project.tags?.join(', ') || ''
      });
    }
    onClose();
  };

  if (!isOpen || !project) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-md mx-auto">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-slate-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <FileText className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-slate-900">编辑项目</h2>
              <p className="text-sm text-slate-500">修改项目基本信息</p>
            </div>
          </div>
          <button
            onClick={handleCancel}
            className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 表单内容 */}
        <div className="p-6 space-y-6">
          {/* 项目名称 */}
          <div className="space-y-2">
            <Label htmlFor="project-name" className="text-sm font-medium text-slate-700">
              项目名称 <span className="text-red-500">*</span>
            </Label>
            <Input
              id="project-name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="输入项目名称"
              className="w-full"
              maxLength={50}
            />
            <p className="text-xs text-slate-500">
              {formData.name.length}/50 字符
            </p>
          </div>

          {/* 项目描述 */}
          <div className="space-y-2">
            <Label htmlFor="project-description" className="text-sm font-medium text-slate-700">
              项目描述
            </Label>
            <Textarea
              id="project-description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="描述你的项目..."
              className="w-full resize-none"
              rows={4}
              maxLength={200}
            />
            <p className="text-xs text-slate-500">
              {formData.description.length}/200 字符
            </p>
          </div>

          {/* 项目标签 */}
          <div className="space-y-2">
            <Label htmlFor="project-tags" className="text-sm font-medium text-slate-700 flex items-center space-x-1">
              <Tag className="w-4 h-4" />
              <span>项目标签</span>
            </Label>
            <Input
              id="project-tags"
              value={formData.tags}
              onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
              placeholder="输入标签，用逗号分隔"
              className="w-full"
            />
            <p className="text-xs text-slate-500">
              用逗号分隔多个标签，例如：React, TypeScript, Web
            </p>
          </div>

          {/* 项目信息 */}
          <div className="bg-slate-50 rounded-lg p-4 space-y-2">
            <h4 className="text-sm font-medium text-slate-700">项目信息</h4>
            <div className="grid grid-cols-2 gap-4 text-xs text-slate-600">
              <div>
                <span className="font-medium">模板：</span>
                <span className="ml-1">{project.template}</span>
              </div>
              <div>
                <span className="font-medium">状态：</span>
                <span className={`ml-1 ${
                  project.status === 'running' ? 'text-green-600' :
                  project.status === 'building' ? 'text-blue-600' :
                  project.status === 'error' ? 'text-red-600' : 'text-slate-600'
                }`}>
                  {project.status === 'running' ? '运行中' :
                   project.status === 'building' ? '构建中' :
                   project.status === 'error' ? '错误' : '空闲'}
                </span>
              </div>
              <div>
                <span className="font-medium">创建时间：</span>
                <span className="ml-1">{new Date(project.createdAt).toLocaleDateString()}</span>
              </div>
              {project.updatedAt && (
                <div>
                  <span className="font-medium">更新时间：</span>
                  <span className="ml-1">{new Date(project.updatedAt).toLocaleDateString()}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-slate-200 bg-slate-50 rounded-b-xl">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isSaving}
            className="px-4 py-2"
          >
            取消
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || !formData.name.trim()}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isSaving ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                保存中...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                保存更改
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default EditProjectModal;
