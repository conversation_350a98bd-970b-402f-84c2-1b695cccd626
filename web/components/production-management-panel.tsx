'use client'

import React, { useState } from 'react';
import { X, Rocket, Activity, Users, Globe, BarChart3, Settings, RefreshCw, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ProductionManagementPanelProps {
  isOpen: boolean;
  onClose: () => void;
  project: any;
}

const ProductionManagementPanel: React.FC<ProductionManagementPanelProps> = ({
  isOpen,
  onClose,
  project
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'deployments' | 'monitoring' | 'settings'>('overview');

  // 模拟生产环境数据
  const productionData = {
    status: 'running',
    version: 'v1.2.3',
    uptime: '15天 8小时',
    visitors: '2,847',
    requests: '45,231',
    errorRate: '0.02%',
    responseTime: '245ms',
    deployments: [
      {
        id: 1,
        version: 'v1.2.3',
        status: 'success',
        deployedAt: '2024-01-15 14:30:00',
        deployedBy: 'admin',
        notes: '修复了用户登录问题'
      },
      {
        id: 2,
        version: 'v1.2.2',
        status: 'success',
        deployedAt: '2024-01-12 10:15:00',
        deployedBy: 'admin',
        notes: '新增了项目管理功能'
      },
      {
        id: 3,
        version: 'v1.2.1',
        status: 'rollback',
        deployedAt: '2024-01-10 16:45:00',
        deployedBy: 'admin',
        notes: '性能优化，后因兼容性问题回滚'
      }
    ]
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-6xl h-[85vh] flex flex-col">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-slate-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
              <Rocket className="w-5 h-5 text-orange-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-slate-900">生产环境管理</h2>
              <p className="text-sm text-slate-500">{project?.name} - 生产环境监控与管理</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className="flex items-center space-x-1 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">
              <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
              <span>生产环境运行中</span>
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 标签页导航 */}
        <div className="flex items-center space-x-1 p-4 border-b border-slate-200 bg-slate-50">
          {[
            { id: 'overview', label: '概览', icon: Activity },
            { id: 'deployments', label: '部署历史', icon: Rocket },
            { id: 'monitoring', label: '监控', icon: BarChart3 },
            { id: 'settings', label: '设置', icon: Settings }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 px-4 py-2 text-sm rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-orange-100 text-orange-700 font-medium'
                    : 'text-slate-600 hover:bg-slate-100'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-auto p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* 状态卡片 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-green-50 rounded-lg p-4 border border-green-100">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-green-800">运行状态</h4>
                    <Activity className="w-4 h-4 text-green-600" />
                  </div>
                  <div className="space-y-1">
                    <div className="text-lg font-semibold text-green-900">正常运行</div>
                    <div className="text-xs text-green-600">运行时间: {productionData.uptime}</div>
                  </div>
                </div>

                <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-blue-800">当前版本</h4>
                    <Rocket className="w-4 h-4 text-blue-600" />
                  </div>
                  <div className="space-y-1">
                    <div className="text-lg font-semibold text-blue-900">{productionData.version}</div>
                    <div className="text-xs text-blue-600">最新稳定版本</div>
                  </div>
                </div>

                <div className="bg-purple-50 rounded-lg p-4 border border-purple-100">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-purple-800">访问量</h4>
                    <Users className="w-4 h-4 text-purple-600" />
                  </div>
                  <div className="space-y-1">
                    <div className="text-lg font-semibold text-purple-900">{productionData.visitors}</div>
                    <div className="text-xs text-purple-600">今日访问用户</div>
                  </div>
                </div>

                <div className="bg-orange-50 rounded-lg p-4 border border-orange-100">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-orange-800">请求数</h4>
                    <Globe className="w-4 h-4 text-orange-600" />
                  </div>
                  <div className="space-y-1">
                    <div className="text-lg font-semibold text-orange-900">{productionData.requests}</div>
                    <div className="text-xs text-orange-600">今日总请求</div>
                  </div>
                </div>
              </div>

              {/* 性能指标 */}
              <div className="bg-white border border-slate-200 rounded-lg">
                <div className="p-4 border-b border-slate-200">
                  <h4 className="text-sm font-medium text-slate-800">性能指标</h4>
                </div>
                <div className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-slate-900">{productionData.responseTime}</div>
                      <div className="text-sm text-slate-500">平均响应时间</div>
                      <div className="mt-2 w-full bg-slate-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: '75%' }}></div>
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-slate-900">{productionData.errorRate}</div>
                      <div className="text-sm text-slate-500">错误率</div>
                      <div className="mt-2 w-full bg-slate-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: '98%' }}></div>
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-slate-900">99.9%</div>
                      <div className="text-sm text-slate-500">可用性</div>
                      <div className="mt-2 w-full bg-slate-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: '99%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 快速操作 */}
              <div className="bg-white border border-slate-200 rounded-lg">
                <div className="p-4 border-b border-slate-200">
                  <h4 className="text-sm font-medium text-slate-800">快速操作</h4>
                </div>
                <div className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button className="flex items-center space-x-3 p-4 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors">
                      <RefreshCw className="w-5 h-5 text-blue-600" />
                      <div className="text-left">
                        <div className="text-sm font-medium text-slate-900">重启应用</div>
                        <div className="text-xs text-slate-500">重启生产环境应用</div>
                      </div>
                    </button>
                    <button className="flex items-center space-x-3 p-4 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors">
                      <AlertTriangle className="w-5 h-5 text-orange-600" />
                      <div className="text-left">
                        <div className="text-sm font-medium text-slate-900">回滚版本</div>
                        <div className="text-xs text-slate-500">回滚到上一个版本</div>
                      </div>
                    </button>
                    <button className="flex items-center space-x-3 p-4 border border-slate-200 rounded-lg hover:bg-slate-50 transition-colors">
                      <BarChart3 className="w-5 h-5 text-green-600" />
                      <div className="text-left">
                        <div className="text-sm font-medium text-slate-900">查看日志</div>
                        <div className="text-xs text-slate-500">查看应用运行日志</div>
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'deployments' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-slate-900">部署历史</h3>
                <Button size="sm" className="bg-orange-600 hover:bg-orange-700 text-white">
                  <Rocket className="w-4 h-4 mr-2" />
                  新建部署
                </Button>
              </div>
              
              <div className="bg-white border border-slate-200 rounded-lg">
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-slate-50">
                      <tr>
                        <th className="text-left py-3 px-4 font-medium text-slate-600">版本</th>
                        <th className="text-left py-3 px-4 font-medium text-slate-600">状态</th>
                        <th className="text-left py-3 px-4 font-medium text-slate-600">部署时间</th>
                        <th className="text-left py-3 px-4 font-medium text-slate-600">部署人</th>
                        <th className="text-left py-3 px-4 font-medium text-slate-600">说明</th>
                        <th className="text-left py-3 px-4 font-medium text-slate-600">操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      {productionData.deployments.map((deployment) => (
                        <tr key={deployment.id} className="border-b border-slate-100">
                          <td className="py-3 px-4 font-medium text-slate-800">{deployment.version}</td>
                          <td className="py-3 px-4">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              deployment.status === 'success' ? 'bg-green-100 text-green-800' :
                              deployment.status === 'rollback' ? 'bg-orange-100 text-orange-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {deployment.status === 'success' ? '成功' :
                               deployment.status === 'rollback' ? '已回滚' : '失败'}
                            </span>
                          </td>
                          <td className="py-3 px-4 text-slate-600">{deployment.deployedAt}</td>
                          <td className="py-3 px-4 text-slate-600">{deployment.deployedBy}</td>
                          <td className="py-3 px-4 text-slate-600">{deployment.notes}</td>
                          <td className="py-3 px-4">
                            <button className="text-orange-600 hover:text-orange-800 text-xs">
                              查看详情
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'monitoring' && (
            <div className="text-center py-12">
              <BarChart3 className="w-12 h-12 text-slate-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-900 mb-2">监控功能</h3>
              <p className="text-slate-500">详细的监控图表和指标分析功能正在开发中...</p>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="text-center py-12">
              <Settings className="w-12 h-12 text-slate-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-900 mb-2">环境设置</h3>
              <p className="text-slate-500">生产环境配置和设置功能正在开发中...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductionManagementPanel;
