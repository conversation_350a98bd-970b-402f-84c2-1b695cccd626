'use client'

import React from 'react';
import { useRouter } from 'next/navigation';
import { MoreVertical, Play, Pause, Settings, Trash2 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { Application, ApplicationStatus } from '@/types';

interface ProjectCardProps {
  application: Application;
  onEdit?: (application: Application) => void;
  onDelete?: (application: Application) => void;
  onStatusChange?: (application: Application, newStatus: ApplicationStatus) => void;
}

// 状态颜色映射
const getStatusColor = (status: ApplicationStatus): string => {
  switch (status) {
    case 'ACTIVE':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'CREATING':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'PAUSED':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'ERROR':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'DELETED':
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

// 状态文本映射
const getStatusText = (status: ApplicationStatus): string => {
  switch (status) {
    case 'ACTIVE':
      return '运行中';
    case 'CREATING':
      return '创建中';
    case 'PAUSED':
      return '已暂停';
    case 'ERROR':
      return '错误';
    case 'DELETED':
      return '已删除';
    default:
      return '未知';
  }
};

// 状态图标映射
const getStatusIcon = (status: ApplicationStatus) => {
  switch (status) {
    case 'ACTIVE':
      return <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />;
    case 'CREATING':
      return <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />;
    case 'PAUSED':
      return <div className="w-2 h-2 bg-yellow-500 rounded-full" />;
    case 'ERROR':
      return <div className="w-2 h-2 bg-red-500 rounded-full" />;
    default:
      return <div className="w-2 h-2 bg-gray-500 rounded-full" />;
  }
};

export function ProjectCard({ 
  application, 
  onEdit, 
  onDelete, 
  onStatusChange 
}: ProjectCardProps) {
  const router = useRouter();

  const handleCardClick = () => {
    router.push(`/workspace/${application.id}`);
  };

  const handleMenuAction = (e: React.MouseEvent, action: () => void) => {
    e.stopPropagation();
    action();
  };

  const canStart = application.status === 'PAUSED';
  const canPause = application.status === 'ACTIVE';

  return (
    <Card className="group cursor-pointer hover:shadow-lg transition-all duration-200 border-slate-200 hover:border-slate-300">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0" onClick={handleCardClick}>
            <CardTitle className="text-lg font-medium truncate text-slate-900 group-hover:text-blue-600 transition-colors">
              {application.name}
            </CardTitle>
          </div>
          <div className="flex items-center space-x-2 ml-2">
            <Badge 
              variant="secondary" 
              className={`${getStatusColor(application.status)} text-xs font-medium border`}
            >
              <div className="flex items-center space-x-1">
                {getStatusIcon(application.status)}
                <span>{getStatusText(application.status)}</span>
              </div>
            </Badge>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {canStart && onStatusChange && (
                  <DropdownMenuItem
                    onClick={(e) => handleMenuAction(e, () => onStatusChange(application, 'ACTIVE'))}
                    className="flex items-center space-x-2"
                  >
                    <Play className="h-4 w-4" />
                    <span>启动项目</span>
                  </DropdownMenuItem>
                )}
                {canPause && onStatusChange && (
                  <DropdownMenuItem
                    onClick={(e) => handleMenuAction(e, () => onStatusChange(application, 'PAUSED'))}
                    className="flex items-center space-x-2"
                  >
                    <Pause className="h-4 w-4" />
                    <span>暂停项目</span>
                  </DropdownMenuItem>
                )}
                {onEdit && (
                  <DropdownMenuItem
                    onClick={(e) => handleMenuAction(e, () => onEdit(application))}
                    className="flex items-center space-x-2"
                  >
                    <Settings className="h-4 w-4" />
                    <span>编辑项目</span>
                  </DropdownMenuItem>
                )}
                {(canStart || canPause || onEdit) && onDelete && <DropdownMenuSeparator />}
                {onDelete && (
                  <DropdownMenuItem
                    onClick={(e) => handleMenuAction(e, () => onDelete(application))}
                    className="flex items-center space-x-2 text-red-600 focus:text-red-600"
                  >
                    <Trash2 className="h-4 w-4" />
                    <span>删除项目</span>
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      
      <CardContent onClick={handleCardClick}>
        <p className="text-sm text-slate-600 mb-4 line-clamp-2 leading-relaxed">
          {application.description || '暂无描述'}
        </p>
        
        <div className="flex items-center justify-between text-xs text-slate-500">
          <span>
            创建于 {new Date(application.createdAt).toLocaleDateString('zh-CN', {
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            })}
          </span>
          {application.updatedAt && application.updatedAt !== application.createdAt && (
            <span>
              更新于 {new Date(application.updatedAt).toLocaleDateString('zh-CN', {
                month: 'short',
                day: 'numeric'
              })}
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export default ProjectCard;
