'use client'

import React, { useState, useRef, useEffect } from 'react';
import { MessageSquare, Send, Bot, User, Loader2 } from 'lucide-react';

interface Project {
  id: string;
  name: string;
  description: string;
  template: string;
  status: 'idle' | 'running' | 'building' | 'error';
  createdAt: string;
}

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: string;
}

interface AIAssistantPanelProps {
  currentProject: Project | null;
  collapsed: boolean;
}

export default function AIAssistantPanel({
  currentProject,
  collapsed
}: AIAssistantPanelProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    // 初始化欢迎消息
    if (currentProject && messages.length === 0) {
      setMessages([
        {
          id: '1',
          type: 'assistant',
          content: `你好！我是你的AI编程助手。我可以帮助你开发 "${currentProject.name}" 项目。你可以问我关于代码、功能实现、调试等任何问题。`,
          timestamp: new Date().toISOString()
        }
      ]);
    }
  }, [currentProject, messages.length]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // 模拟AI响应
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: generateAIResponse(userMessage.content),
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, aiResponse]);
      setIsLoading(false);
    }, 1000 + Math.random() * 2000);
  };

  const generateAIResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();
    
    if (input.includes('组件') || input.includes('component')) {
      return `我可以帮你创建React组件。这里是一个基础的组件模板：

\`\`\`tsx
import React from 'react';

interface Props {
  // 定义组件属性
}

export default function MyComponent({ }: Props) {
  return (
    <div>
      {/* 组件内容 */}
    </div>
  );
}
\`\`\`

你想创建什么类型的组件？`;
    }
    
    if (input.includes('api') || input.includes('接口')) {
      return `对于API开发，我建议使用Next.js的API Routes。这里是一个示例：

\`\`\`typescript
// pages/api/example.ts
import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method === 'GET') {
    res.status(200).json({ message: 'Hello API' });
  } else {
    res.setHeader('Allow', ['GET']);
    res.status(405).end(\`Method \${req.method} Not Allowed\`);
  }
}
\`\`\`

需要我帮你实现特定的API功能吗？`;
    }
    
    if (input.includes('样式') || input.includes('css') || input.includes('tailwind')) {
      return `对于样式，我推荐使用Tailwind CSS。这里是一些常用的样式类：

- 布局: \`flex\`, \`grid\`, \`container\`
- 间距: \`p-4\`, \`m-2\`, \`space-x-4\`
- 颜色: \`bg-blue-500\`, \`text-white\`
- 响应式: \`md:flex\`, \`lg:grid-cols-3\`

你想要实现什么样的样式效果？`;
    }
    
    if (input.includes('数据库') || input.includes('database')) {
      return `对于数据库操作，我建议使用Prisma ORM。这里是基本的设置：

1. 安装Prisma: \`npm install prisma @prisma/client\`
2. 初始化: \`npx prisma init\`
3. 定义模型在 \`schema.prisma\`
4. 生成客户端: \`npx prisma generate\`

需要我帮你设计数据模型吗？`;
    }
    
    // 默认响应
    const responses = [
      '我理解你的问题。让我为你提供一个解决方案...',
      '这是一个很好的问题！我来帮你分析一下...',
      '根据你的需求，我建议这样实现...',
      '让我为你提供一些代码示例和最佳实践...'
    ];
    
    return responses[Math.floor(Math.random() * responses.length)];
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (collapsed) {
    return null;
  }

  return (
    <div className="h-full bg-white border-l border-slate-200 flex flex-col">
      {/* AI助手头部 */}
      <div className="p-4 border-b border-slate-200 bg-gradient-to-r from-slate-50 to-white">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <Bot className="w-4 h-4 text-blue-600" />
          </div>
          <div>
            <h3 className="text-sm font-semibold text-slate-900">AI编程助手</h3>
            {currentProject && (
              <p className="text-xs text-slate-500">{currentProject.name}</p>
            )}
          </div>
        </div>
      </div>

      {/* 对话内容区域 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {currentProject ? (
          <>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-lg px-3 py-2 ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-slate-100 text-slate-900'
                  }`}
                >
                  <div className="flex items-start space-x-2">
                    {message.type === 'assistant' && (
                      <Bot className="w-4 h-4 mt-0.5 text-slate-600" />
                    )}
                    {message.type === 'user' && (
                      <User className="w-4 h-4 mt-0.5 text-white" />
                    )}
                    <div className="flex-1">
                      <div className="text-sm whitespace-pre-wrap">
                        {message.content}
                      </div>
                      <div className={`text-xs mt-1 ${
                        message.type === 'user' ? 'text-blue-200' : 'text-slate-500'
                      }`}>
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-slate-100 rounded-lg px-3 py-2">
                  <div className="flex items-center space-x-2">
                    <Bot className="w-4 h-4 text-slate-600" />
                    <Loader2 className="w-4 h-4 animate-spin text-slate-600" />
                    <span className="text-sm text-slate-600">AI正在思考...</span>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </>
        ) : (
          <div className="h-full flex items-center justify-center text-slate-500">
            <div className="text-center">
              <MessageSquare className="w-12 h-12 mx-auto mb-3 text-slate-300" />
              <p className="text-sm font-medium mb-1">选择一个项目</p>
              <p className="text-xs text-slate-400">开始与AI助手对话</p>
            </div>
          </div>
        )}
      </div>

      {/* 输入区域 */}
      {currentProject && (
        <div className="p-4 border-t border-slate-200">
          <div className="flex space-x-2">
            <textarea
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="询问AI助手..."
              className="flex-1 px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={1}
              disabled={isLoading}
            />
            <button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              className="px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-slate-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center justify-center"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </button>
          </div>
          <div className="mt-2 text-xs text-slate-500">
            按 Enter 发送，Shift + Enter 换行
          </div>
        </div>
      )}
    </div>
  );
}
