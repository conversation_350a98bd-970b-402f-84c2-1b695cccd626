'use client'

import React, { useState, useRef, useEffect } from 'react';
import { 
  Terminal, 
  Trash2, 
  Download, 
  Search, 
  Filter,
  ChevronDown,
  AlertCircle,
  Info,
  AlertTriangle,
  X
} from 'lucide-react';

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'log' | 'debug';
  message: string;
  source: 'frontend' | 'backend' | 'system';
  details?: string;
}

interface LogViewerProps {
  logs: LogEntry[];
  onClearLogs?: () => void;
  className?: string;
  maxHeight?: number;
}

export default function LogViewer({
  logs,
  onClearLogs,
  className = '',
  maxHeight = 300
}: LogViewerProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [levelFilter, setLevelFilter] = useState<string>('all');
  const [sourceFilter, setSourceFilter] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [autoScroll, setAutoScroll] = useState(true);
  const [selectedLog, setSelectedLog] = useState<LogEntry | null>(null);
  const logContainerRef = useRef<HTMLDivElement>(null);
  const endRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && endRef.current) {
      endRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs, autoScroll]);

  // 过滤日志
  const filteredLogs = logs.filter(log => {
    const matchesSearch = searchTerm === '' || 
      log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.source.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesLevel = levelFilter === 'all' || log.level === levelFilter;
    const matchesSource = sourceFilter === 'all' || log.source === sourceFilter;
    
    return matchesSearch && matchesLevel && matchesSource;
  });

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'warn':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'info':
        return <Info className="w-4 h-4 text-blue-500" />;
      default:
        return <Terminal className="w-4 h-4 text-slate-500" />;
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'warn':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'info':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'debug':
        return 'text-purple-600 bg-purple-50 border-purple-200';
      default:
        return 'text-slate-600 bg-slate-50 border-slate-200';
    }
  };

  const getSourceColor = (source: string) => {
    switch (source) {
      case 'frontend':
        return 'bg-green-100 text-green-800';
      case 'backend':
        return 'bg-blue-100 text-blue-800';
      case 'system':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-slate-100 text-slate-800';
    }
  };

  const handleExportLogs = () => {
    const logText = filteredLogs.map(log => 
      `[${new Date(log.timestamp).toLocaleString()}] [${log.level.toUpperCase()}] [${log.source}] ${log.message}`
    ).join('\n');
    
    const blob = new Blob([logText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `logs-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleScroll = () => {
    if (logContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = logContainerRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
      setAutoScroll(isAtBottom);
    }
  };

  return (
    <div className={`flex flex-col bg-white border border-slate-200 rounded-lg ${className}`}>
      {/* 工具栏 */}
      <div className="flex items-center justify-between px-4 py-3 border-b border-slate-200 bg-slate-50">
        <div className="flex items-center space-x-3">
          <Terminal className="w-4 h-4 text-slate-600" />
          <h3 className="text-sm font-medium text-slate-900">控制台日志</h3>
          <span className="text-xs text-slate-500 bg-slate-200 px-2 py-1 rounded">
            {filteredLogs.length} 条
          </span>
        </div>

        <div className="flex items-center space-x-2">
          {/* 搜索 */}
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-slate-400" />
            <input
              type="text"
              placeholder="搜索日志..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-7 pr-3 py-1 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>

          {/* 过滤器 */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center space-x-1 px-2 py-1 text-xs font-medium rounded transition-colors ${
              showFilters ? 'bg-blue-100 text-blue-700' : 'text-slate-600 hover:bg-slate-100'
            }`}
          >
            <Filter className="w-3 h-3" />
            <span>过滤</span>
            <ChevronDown className={`w-3 h-3 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>

          {/* 自动滚动 */}
          <label className="flex items-center space-x-1 text-xs text-slate-600">
            <input
              type="checkbox"
              checked={autoScroll}
              onChange={(e) => setAutoScroll(e.target.checked)}
              className="w-3 h-3"
            />
            <span>自动滚动</span>
          </label>

          {/* 导出 */}
          <button
            onClick={handleExportLogs}
            className="flex items-center space-x-1 px-2 py-1 text-xs font-medium text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded transition-colors"
            title="导出日志"
          >
            <Download className="w-3 h-3" />
          </button>

          {/* 清空 */}
          <button
            onClick={onClearLogs}
            className="flex items-center space-x-1 px-2 py-1 text-xs font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
            title="清空日志"
          >
            <Trash2 className="w-3 h-3" />
          </button>
        </div>
      </div>

      {/* 过滤器面板 */}
      {showFilters && (
        <div className="px-4 py-3 border-b border-slate-200 bg-slate-50">
          <div className="flex items-center space-x-4">
            <div>
              <label className="block text-xs font-medium text-slate-700 mb-1">日志级别</label>
              <select
                value={levelFilter}
                onChange={(e) => setLevelFilter(e.target.value)}
                className="px-2 py-1 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">全部</option>
                <option value="error">错误</option>
                <option value="warn">警告</option>
                <option value="info">信息</option>
                <option value="log">日志</option>
                <option value="debug">调试</option>
              </select>
            </div>

            <div>
              <label className="block text-xs font-medium text-slate-700 mb-1">来源</label>
              <select
                value={sourceFilter}
                onChange={(e) => setSourceFilter(e.target.value)}
                className="px-2 py-1 text-xs border border-slate-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">全部</option>
                <option value="frontend">前端</option>
                <option value="backend">后端</option>
                <option value="system">系统</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* 日志内容 */}
      <div 
        ref={logContainerRef}
        className="flex-1 overflow-y-auto font-mono text-xs"
        style={{ maxHeight: `${maxHeight}px` }}
        onScroll={handleScroll}
      >
        {filteredLogs.length === 0 ? (
          <div className="flex items-center justify-center h-32 text-slate-500">
            <div className="text-center">
              <Terminal className="w-8 h-8 mx-auto mb-2 text-slate-300" />
              <p>暂无日志信息</p>
            </div>
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {filteredLogs.map((log) => (
              <div
                key={log.id}
                className={`flex items-start space-x-2 p-2 rounded border cursor-pointer hover:bg-slate-50 transition-colors ${
                  selectedLog?.id === log.id ? getLevelColor(log.level) : 'border-transparent'
                }`}
                onClick={() => setSelectedLog(selectedLog?.id === log.id ? null : log)}
              >
                <div className="flex-shrink-0 mt-0.5">
                  {getLevelIcon(log.level)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-slate-500">
                      {new Date(log.timestamp).toLocaleTimeString()}
                    </span>
                    <span className={`px-1.5 py-0.5 rounded text-xs font-medium ${getSourceColor(log.source)}`}>
                      {log.source}
                    </span>
                    <span className="text-slate-400 text-xs font-medium">
                      [{log.level.toUpperCase()}]
                    </span>
                  </div>
                  
                  <div className="text-slate-900 break-words">
                    {log.message}
                  </div>
                  
                  {selectedLog?.id === log.id && log.details && (
                    <div className="mt-2 p-2 bg-slate-100 rounded text-slate-700">
                      {log.details}
                    </div>
                  )}
                </div>
              </div>
            ))}
            <div ref={endRef} />
          </div>
        )}
      </div>

      {/* 日志详情模态框 */}
      {selectedLog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-96 overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-900">日志详情</h3>
              <button
                onClick={() => setSelectedLog(null)}
                className="text-slate-400 hover:text-slate-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-3 text-sm">
              <div>
                <span className="font-medium text-slate-700">时间: </span>
                <span className="text-slate-900">{new Date(selectedLog.timestamp).toLocaleString()}</span>
              </div>
              <div>
                <span className="font-medium text-slate-700">级别: </span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${getLevelColor(selectedLog.level)}`}>
                  {selectedLog.level.toUpperCase()}
                </span>
              </div>
              <div>
                <span className="font-medium text-slate-700">来源: </span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${getSourceColor(selectedLog.source)}`}>
                  {selectedLog.source}
                </span>
              </div>
              <div>
                <span className="font-medium text-slate-700">消息: </span>
                <div className="mt-1 p-3 bg-slate-100 rounded font-mono text-xs">
                  {selectedLog.message}
                </div>
              </div>
              {selectedLog.details && (
                <div>
                  <span className="font-medium text-slate-700">详情: </span>
                  <div className="mt-1 p-3 bg-slate-100 rounded font-mono text-xs">
                    {selectedLog.details}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
