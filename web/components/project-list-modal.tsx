'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { X, Search, FolderOpen } from 'lucide-react';
import { applicationApi } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import type { Application, ApplicationStatus } from '@/types';

interface ProjectListModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// 状态颜色映射
const getStatusColor = (status: ApplicationStatus): string => {
  switch (status) {
    case 'ACTIVE':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'CREATING':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'PAUSED':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'ERROR':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'DELETED':
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

// 状态文本映射
const getStatusText = (status: ApplicationStatus): string => {
  switch (status) {
    case 'ACTIVE':
      return '运行中';
    case 'CREATING':
      return '创建中';
    case 'PAUSED':
      return '已暂停';
    case 'ERROR':
      return '错误';
    case 'DELETED':
      return '已删除';
    default:
      return '未知';
  }
};

// 状态图标
const getStatusIcon = (status: ApplicationStatus) => {
  switch (status) {
    case 'ACTIVE':
      return <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />;
    case 'CREATING':
      return <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />;
    case 'PAUSED':
      return <div className="w-2 h-2 bg-yellow-500 rounded-full" />;
    case 'ERROR':
      return <div className="w-2 h-2 bg-red-500 rounded-full" />;
    default:
      return <div className="w-2 h-2 bg-gray-500 rounded-full" />;
  }
};

export function ProjectListModal({ open, onOpenChange }: ProjectListModalProps) {
  const router = useRouter();
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // 获取应用列表
  const fetchApplications = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await applicationApi.getApplications({
        page: 1,
        pageSize: 50
      });
      setApplications(response.data || []);
    } catch (err: any) {
      console.error('获取项目列表失败:', err);
      setError(err.message || '获取项目列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 当弹窗打开时获取数据
  useEffect(() => {
    if (open) {
      fetchApplications();
    }
  }, [open]);

  // 过滤应用
  const filteredApplications = applications.filter(app =>
    app.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (app.description && app.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // 处理项目点击
  const handleProjectClick = (application: Application) => {
    onOpenChange(false);
    router.push(`/workspace/${application.id}`);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl h-[700px] max-h-[85vh] p-0">
        <div className="flex flex-col h-full">
          {/* 标题和搜索栏 - 固定在顶部 */}
          <div className="flex-shrink-0 px-6 py-4 border-b border-slate-200 bg-white">
            {/* 标题 */}
            <div className="flex items-center space-x-3 mb-3">
              <FolderOpen className="w-5 h-5 text-blue-600" />
              <span className="text-lg font-semibold">我的项目</span>
              <Badge variant="secondary" className="ml-2 text-sm">
                {filteredApplications.length} 个项目
              </Badge>
            </div>

            {/* 搜索框 */}
            <div className="relative max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <Input
                placeholder="搜索项目名称或描述..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-full"
              />
            </div>
          </div>

          {/* 内容区域 - 可滚动 */}
          <div className="flex-1 overflow-hidden bg-slate-50">
            <div className="h-full overflow-y-auto px-6 py-4">
            {/* 错误提示 */}
            {error && (
              <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center justify-between">
                  <p className="text-red-800">{error}</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={fetchApplications}
                    className="text-red-600 border-red-300 hover:bg-red-50"
                  >
                    重试
                  </Button>
                </div>
              </div>
            )}

            {/* 加载状态 */}
            {loading && (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-slate-600">加载中...</p>
                </div>
              </div>
            )}

            {/* 项目列表 */}
            {!loading && (
              <div>
                {filteredApplications.length === 0 ? (
                  <div className="text-center py-16">
                    <div className="w-20 h-20 mx-auto mb-6 bg-white rounded-full flex items-center justify-center shadow-sm border border-slate-200">
                      <FolderOpen className="w-8 h-8 text-slate-400" />
                    </div>
                    <h3 className="text-xl font-medium text-slate-900 mb-3">
                      {searchTerm ? '没有找到匹配的项目' : '还没有项目'}
                    </h3>
                    <p className="text-slate-500 text-lg">
                      {searchTerm ? '尝试调整搜索条件' : '您还没有创建任何项目'}
                    </p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                    {filteredApplications.map((application) => (
                      <div
                        key={application.id}
                        className="group bg-white p-6 border border-slate-200 rounded-xl hover:border-blue-300 hover:shadow-lg transition-all duration-200 cursor-pointer"
                        onClick={() => handleProjectClick(application)}
                      >
                        {/* 卡片头部 */}
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1 min-w-0">
                            <h3 className="text-lg font-semibold text-slate-900 truncate group-hover:text-blue-600 transition-colors mb-1">
                              {application.name}
                            </h3>
                            {application.framework && (
                              <div className="flex items-center space-x-1 mt-1">
                                <span className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-md font-medium">
                                  {application.framework.name}
                                </span>
                              </div>
                            )}
                          </div>
                          <Badge
                            variant="secondary"
                            className={`${getStatusColor(application.status)} text-xs font-medium border ml-3 flex-shrink-0`}
                          >
                            <div className="flex items-center space-x-1.5">
                              {getStatusIcon(application.status)}
                              <span>{getStatusText(application.status)}</span>
                            </div>
                          </Badge>
                        </div>

                        {/* 项目描述 */}
                        <p className="text-sm text-slate-600 mb-4 leading-relaxed min-h-[3.75rem] overflow-hidden" style={{
                          display: '-webkit-box',
                          WebkitLineClamp: 3,
                          WebkitBoxOrient: 'vertical'
                        }}>
                          {application.description || '暂无项目描述'}
                        </p>

                        {/* 卡片底部信息 */}
                        <div className="flex items-center justify-between text-xs text-slate-500 pt-3 border-t border-slate-100">
                          <span className="flex items-center space-x-1">
                            <span>创建于</span>
                            <span className="font-medium">
                              {new Date(application.createdAt).toLocaleDateString('zh-CN', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                              })}
                            </span>
                          </span>
                          {application.updatedAt && application.updatedAt !== application.createdAt && (
                            <span className="flex items-center space-x-1">
                              <span>更新于</span>
                              <span className="font-medium">
                                {new Date(application.updatedAt).toLocaleDateString('zh-CN', {
                                  month: 'short',
                                  day: 'numeric'
                                })}
                              </span>
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default ProjectListModal;
