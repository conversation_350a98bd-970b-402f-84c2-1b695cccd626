'use client'

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Play,
  Rocket,
  Database,
  MessageSquare,
  Folder,
  Edit3,
  ChevronDown,
  Pause
} from 'lucide-react';

interface Project {
  id: string;
  name: string;
  description: string;
  template: string;
  status: 'idle' | 'running' | 'building' | 'error';
  createdAt: string;
  updatedAt?: string;
  tags?: string[];
}

interface WorkspaceHeaderProps {
  currentProject: Project | null;
  onRunProject: () => void;
  onSaveProject: () => void;
  onShowDatabase: () => void;
  onToggleAIPanel: () => void;
  aiPanelCollapsed: boolean;
  sidebarVisible?: boolean;
  setSidebarVisible?: (visible: boolean) => void;
  onProjectUpdate?: (project: Project) => void;
  onEditProject?: () => void;
  onShowUnifiedDeployModal?: () => void;
  onShowProductionPanel?: () => void;
}

export default function WorkspaceHeader({
  currentProject,
  onRunProject,
  onSaveProject,
  onShowDatabase,
  onToggleAIPanel,
  aiPanelCollapsed,
  sidebarVisible = false,
  setSidebarVisible,
  onProjectUpdate,
  onEditProject,
  onShowUnifiedDeployModal,
  onShowProductionPanel
}: WorkspaceHeaderProps) {
  const router = useRouter();
  const [devEnvironmentStatus, setDevEnvironmentStatus] = useState<'running' | 'stopped'>('running');
  const [showUnifiedDeployModal, setShowUnifiedDeployModal] = useState(false);
  const [showProductionPanel, setShowProductionPanel] = useState(false);

  const handleBackToHome = () => {
    router.push('/');
  };

  const handleDeploy = () => {
    setShowUnifiedDeployModal(true);
  };

  const toggleDevEnvironment = () => {
    setDevEnvironmentStatus(prev => prev === 'running' ? 'stopped' : 'running');
  };

  return (
    <div className="h-12 bg-white border-b border-slate-200 flex items-center justify-between px-4">
      {/* 左侧导航 */}
      <div className="flex items-center space-x-3">
        <button
          onClick={handleBackToHome}
          className="p-1.5 text-slate-500 hover:text-slate-700 hover:bg-slate-100 rounded transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
        </button>

        {currentProject && (
          <>
            <div className="h-5 w-px bg-slate-300"></div>

            {/* 项目标题区域 - 点击显示项目侧边栏 */}
            <button
              onClick={() => setSidebarVisible && setSidebarVisible(!sidebarVisible)}
              className="flex items-center space-x-1.5 px-3 py-1.5 text-sm font-medium text-slate-800 hover:bg-slate-100 rounded-lg transition-colors"
            >
              <Folder className="w-4 h-4 text-slate-600" />
              <h1 className="text-sm font-medium">{currentProject.name}</h1>
              <ChevronDown className={`w-3.5 h-3.5 text-slate-500 transition-transform ${sidebarVisible ? 'rotate-180' : ''}`} />
            </button>

            {/* 编辑项目按钮 */}
            <button
              onClick={onEditProject}
              className="p-1.5 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded transition-colors"
              title="编辑项目信息"
            >
              <Edit3 className="w-3.5 h-3.5" />
            </button>

            {/* 开发环境控制区域 */}
            <div className="flex items-center space-x-2 ml-4">
              {/* 开发环境状态切换 */}
              <button
                onClick={toggleDevEnvironment}
                className={`flex items-center space-x-1 px-2 py-1 text-xs border rounded transition-colors ${
                  devEnvironmentStatus === 'running'
                    ? 'border-green-200 bg-green-50 text-green-700 hover:bg-green-100'
                    : 'border-slate-200 bg-slate-50 text-slate-700 hover:bg-slate-100'
                }`}
                title={devEnvironmentStatus === 'running' ? '停止开发环境' : '启动开发环境'}
              >
                {devEnvironmentStatus === 'running' ? (
                  <>
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span>运行中</span>
                    <Pause className="w-3 h-3" />
                  </>
                ) : (
                  <>
                    <div className="w-2 h-2 bg-slate-400 rounded-full"></div>
                    <span>已停止</span>
                    <Play className="w-3 h-3" />
                  </>
                )}
              </button>

              {/* 开发环境数据库按钮 */}
              <button
                onClick={onShowDatabase}
                className="flex items-center space-x-1 px-2 py-1 text-xs border border-green-200 bg-green-50 text-green-700 hover:bg-green-100 rounded transition-colors"
                title="开发环境数据库"
              >
                <Database className="w-3 h-3" />
                <span>数据库</span>
                <span className="text-[10px] bg-green-200 text-green-800 px-1 rounded font-medium">DEV</span>
              </button>
            </div>
          </>
        )}
      </div>

      {/* 右侧工具按钮 */}
      <div className="flex items-center space-x-2">
        {currentProject && (
          <>
            {/* 生产环境管理按钮 */}
            <button
              onClick={onShowProductionPanel}
              className="flex items-center space-x-1 px-2 py-1 text-xs border border-orange-200 bg-orange-50 text-orange-700 hover:bg-orange-100 rounded transition-colors"
              title="生产环境管理"
            >
              <Rocket className="w-3 h-3" />
              <span>生产环境</span>
              <span className="text-[10px] bg-orange-200 text-orange-800 px-1 rounded font-medium">PROD</span>
            </button>

            {/* 发布新版本按钮 */}
            <button
              onClick={onShowUnifiedDeployModal}
              className="flex items-center space-x-1 px-2 py-1 text-xs border border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100 rounded transition-colors"
              title="发布新版本"
            >
              <Rocket className="w-3 h-3" />
              <span>发布版本</span>
            </button>

            {/* AI助手切换按钮 */}
            <button
              onClick={onToggleAIPanel}
              className={`flex items-center space-x-1 px-2 py-1 text-xs border rounded transition-colors ${
                !aiPanelCollapsed
                  ? 'border-purple-200 bg-purple-50 text-purple-700 hover:bg-purple-100'
                  : 'border-slate-200 text-slate-700 hover:bg-slate-50'
              }`}
              title={aiPanelCollapsed ? '显示AI助手' : '隐藏AI助手'}
            >
              <MessageSquare className="w-3 h-3" />
              <span>{aiPanelCollapsed ? '显示AI' : '隐藏AI'}</span>
            </button>
          </>
        )}
      </div>
    </div>
  );
}
