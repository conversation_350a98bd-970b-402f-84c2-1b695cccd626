'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createPortal } from 'react-dom';
import {
  Folder,
  Edit3,
  Play,
  X,
  Save,
  Trash2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';


interface Project {
  id: string;
  name: string;
  description: string;
  template: string;
  status: 'idle' | 'running' | 'building' | 'error';
  createdAt: string;
  updatedAt?: string;
  tags?: string[];
}

interface ProjectSidebarProps {
  projects: Project[];
  currentProject: Project | null;
  visible: boolean;
  onClose: () => void;
  onProjectSelect: (project: Project) => void;
  onProjectUpdate: (project: Project) => void;
}

const ProjectSidebar: React.FC<ProjectSidebarProps> = ({
  projects,
  currentProject,
  visible,
  onClose,
  onProjectSelect,
  onProjectUpdate
}) => {
  const router = useRouter();
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [editForm, setEditForm] = useState({ name: '', description: '' });

  // 键盘支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && showEditModal) {
        handleCancelEdit();
      }
    };

    if (showEditModal) {
      document.addEventListener('keydown', handleKeyDown);
      // 防止背景滚动
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [showEditModal]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-green-600';
      case 'building': return 'text-blue-600';
      case 'error': return 'text-red-600';
      default: return 'text-slate-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'running': return '运行中';
      case 'building': return '构建中';
      case 'error': return '错误';
      default: return '空闲';
    }
  };

  const handleEditProject = (project: Project) => {
    setEditingProject(project);
    setEditForm({ name: project.name, description: project.description });
    setShowEditModal(true);
  };

  const handleSaveEdit = () => {
    if (editingProject) {
      const updatedProject = {
        ...editingProject,
        name: editForm.name,
        description: editForm.description,
        updatedAt: new Date().toISOString()
      };
      onProjectUpdate(updatedProject);
      setShowEditModal(false);
      setEditingProject(null);
    }
  };

  const handleCancelEdit = () => {
    setShowEditModal(false);
    setEditingProject(null);
    setEditForm({ name: '', description: '' });
  };

  const handleProjectClick = (project: Project) => {
    router.push(`/workspace/${project.id}`);
    onProjectSelect(project);
    onClose();
  };

  if (!visible) return null;

  return (
    <>
      {/* 项目抽屉浮窗 */}
      <div
        className="fixed left-0 top-0 h-full w-80 bg-white shadow-xl z-50 flex flex-col transform transition-all duration-300 ease-out"
        onMouseLeave={(e) => {
          // 如果编辑模态框打开，不要关闭侧边栏
          if (!showEditModal) {
            onClose();
          }
        }}
      >
        {/* 抽屉头部 */}
        <div className="p-3 border-b border-slate-200 bg-gradient-to-r from-slate-50 to-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Folder className="w-4 h-4 text-slate-600" />
              <h3 className="text-sm font-medium text-slate-900">项目列表</h3>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 项目列表 */}
        <div className="flex-1 overflow-y-auto p-3">
          <div className="space-y-2">
            {projects.map((project) => (
              <div
                key={project.id}
                className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                  currentProject?.id === project.id
                    ? 'border-blue-200 bg-blue-50 shadow-sm'
                    : 'border-slate-200 hover:border-slate-300 hover:bg-slate-50'
                }`}
                onClick={() => handleProjectClick(project)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-slate-900 truncate">
                      {project.name}
                    </h4>
                    <p className="text-xs text-slate-500 mt-1 line-clamp-2">
                      {project.description}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs px-2 py-1 bg-slate-100 text-slate-600 rounded">
                        {project.template}
                      </span>
                      <span className={`text-xs ${getStatusColor(project.status)}`}>
                        {getStatusText(project.status)}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1 ml-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditProject(project);
                      }}
                      className="h-6 w-6 p-0"
                    >
                      <Edit3 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 编辑项目模态框 - 使用 Portal 渲染到 body */}
      {showEditModal && typeof window !== 'undefined' && createPortal(
        <div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-[9999] p-4"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              handleCancelEdit();
            }
          }}
        >
          <div
            className="bg-white rounded-xl shadow-2xl w-full max-w-md mx-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 头部 */}
            <div className="flex items-center justify-between p-6 border-b border-slate-200">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Edit3 className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-slate-900">编辑项目</h2>
                  <p className="text-sm text-slate-500">修改项目基本信息</p>
                </div>
              </div>
              <button
                onClick={handleCancelEdit}
                className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* 表单内容 */}
            <div className="p-6 space-y-6">
              {/* 项目名称 */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-700">
                  项目名称 <span className="text-red-500">*</span>
                </label>
                <Input
                  value={editForm.name}
                  onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                  placeholder="输入项目名称"
                  className="w-full"
                  maxLength={50}
                />
                <p className="text-xs text-slate-500">
                  {editForm.name.length}/50 字符
                </p>
              </div>

              {/* 项目描述 */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-700">项目描述</label>
                <Textarea
                  value={editForm.description}
                  onChange={(e) => setEditForm({ ...editForm, description: e.target.value })}
                  placeholder="描述你的项目..."
                  className="w-full resize-none"
                  rows={4}
                  maxLength={200}
                />
                <p className="text-xs text-slate-500">
                  {editForm.description.length}/200 字符
                </p>
              </div>

              {/* 项目信息 */}
              {editingProject && (
                <div className="bg-slate-50 rounded-lg p-4 space-y-2">
                  <h4 className="text-sm font-medium text-slate-700">项目信息</h4>
                  <div className="grid grid-cols-2 gap-4 text-xs text-slate-600">
                    <div>
                      <span className="font-medium">模板：</span>
                      <span className="ml-1">{editingProject.template}</span>
                    </div>
                    <div>
                      <span className="font-medium">状态：</span>
                      <span className={`ml-1 ${
                        editingProject.status === 'running' ? 'text-green-600' :
                        editingProject.status === 'building' ? 'text-blue-600' :
                        editingProject.status === 'error' ? 'text-red-600' : 'text-slate-600'
                      }`}>
                        {editingProject.status === 'running' ? '运行中' :
                         editingProject.status === 'building' ? '构建中' :
                         editingProject.status === 'error' ? '错误' : '空闲'}
                      </span>
                    </div>
                    <div>
                      <span className="font-medium">创建时间：</span>
                      <span className="ml-1">{new Date(editingProject.createdAt).toLocaleDateString()}</span>
                    </div>
                    {editingProject.updatedAt && (
                      <div>
                        <span className="font-medium">更新时间：</span>
                        <span className="ml-1">{new Date(editingProject.updatedAt).toLocaleDateString()}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* 底部按钮 */}
            <div className="flex items-center justify-end space-x-3 p-6 border-t border-slate-200 bg-slate-50 rounded-b-xl">
              <Button
                variant="outline"
                onClick={handleCancelEdit}
                className="px-4 py-2"
              >
                取消
              </Button>
              <Button
                onClick={handleSaveEdit}
                disabled={!editForm.name.trim()}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Save className="w-4 h-4 mr-2" />
                保存更改
              </Button>
            </div>
          </div>
        </div>,
        document.body
      )}
    </>
  );
};

export default ProjectSidebar;
