'use client'

import React, { useState } from 'react';
import { X, Rocket, Save, Clock, CheckCircle, AlertCircle, MoreVertical, Edit, Trash2, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface Project {
  id: string;
  name: string;
  description: string;
  template: string;
  status: 'idle' | 'running' | 'building' | 'error';
  createdAt: string;
  updatedAt?: string;
  tags?: string[];
}

interface Version {
  id: string;
  version: string;
  description: string;
  createdAt: string;
  status: 'saved' | 'deployed' | 'deploying';
}

interface UnifiedDeployModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project | null;
}

const UnifiedDeployModal: React.FC<UnifiedDeployModalProps> = ({
  isOpen,
  onClose,
  project
}) => {
  const [version, setVersion] = useState('');
  const [description, setDescription] = useState('');
  const [editingVersion, setEditingVersion] = useState<Version | null>(null);
  const [editDescription, setEditDescription] = useState('');
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  const [isDeploying, setIsDeploying] = useState(false);

  // 模拟版本数据
  const [versions, setVersions] = useState<Version[]>([
    {
      id: '1',
      version: 'v1.2.0',
      description: '添加用户认证功能，修复若干 bug',
      createdAt: '2024-01-15 14:30',
      status: 'deployed'
    },
    {
      id: '2',
      version: 'v1.1.0',
      description: '优化界面交互，提升性能',
      createdAt: '2024-01-10 09:15',
      status: 'saved'
    },
    {
      id: '3',
      version: 'v1.0.0',
      description: '初始版本发布',
      createdAt: '2024-01-05 16:45',
      status: 'saved'
    },
    {
      id: 'v4',
      version: 'v0.9.0',
      description: '测试版本，添加基础功能模块',
      createdAt: '2024-01-01 10:00',
      status: 'saved'
    },
    {
      id: 'v5',
      version: 'v0.8.0',
      description: '修复登录问题，优化数据库查询',
      createdAt: '2023-12-28 15:30',
      status: 'saved'
    },
    {
      id: 'v6',
      version: 'v0.7.0',
      description: '添加文件上传功能，支持多种格式',
      createdAt: '2023-12-25 11:20',
      status: 'saved'
    },
    {
      id: '4',
      version: 'v0.9.0',
      description: '测试版本，添加基础功能模块',
      createdAt: '2024-01-01 10:00',
      status: 'saved'
    },
    {
      id: '5',
      version: 'v0.8.0',
      description: '修复登录问题，优化数据库查询',
      createdAt: '2023-12-28 15:30',
      status: 'saved'
    },
    {
      id: '6',
      version: 'v0.7.0',
      description: '添加文件上传功能，支持多种格式',
      createdAt: '2023-12-25 11:20',
      status: 'saved'
    },
    {
      id: '7',
      version: 'v0.6.0',
      description: '重构前端组件，提升代码可维护性',
      createdAt: '2023-12-20 09:45',
      status: 'saved'
    },
    {
      id: '8',
      version: 'v0.5.0',
      description: '集成第三方支付系统，完善订单流程',
      createdAt: '2023-12-15 14:15',
      status: 'saved'
    }
  ]);

  if (!isOpen) return null;

  const handleSaveVersion = () => {
    if (!version.trim()) {
      alert('请输入版本号');
      return;
    }
    if (!description.trim()) {
      alert('请输入版本描述');
      return;
    }

    // 创建新版本
    const newVersion: Version = {
      id: Date.now().toString(),
      version: version.trim(),
      description: description.trim(),
      createdAt: new Date().toLocaleString('zh-CN'),
      status: 'saved'
    };

    setVersions([newVersion, ...versions]);
    setVersion('');
    setDescription('');

    console.log('保存版本:', newVersion);
  };

  const handleDeploy = async (versionItem: Version) => {
    setIsDeploying(true);
    setOpenMenuId(null);

    // 更新版本状态为部署中
    setVersions(versions.map(v =>
      v.id === versionItem.id ? { ...v, status: 'deploying' } : v
    ));

    // 模拟部署过程
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 更新版本状态
    setVersions(versions.map(v => ({
      ...v,
      status: v.id === versionItem.id ? 'deployed' : (v.status === 'deployed' ? 'saved' : v.status)
    })));

    setIsDeploying(false);

    console.log('部署版本:', versionItem);
  };

  const handleEditVersion = (versionItem: Version) => {
    setEditingVersion(versionItem);
    setEditDescription(versionItem.description);
    setOpenMenuId(null);
  };

  const handleSaveEdit = () => {
    if (!editDescription.trim()) {
      alert('请输入版本描述');
      return;
    }

    setVersions(versions.map(v =>
      v.id === editingVersion?.id
        ? { ...v, description: editDescription.trim() }
        : v
    ));

    setEditingVersion(null);
    setEditDescription('');
  };

  const handleCancelEdit = () => {
    setEditingVersion(null);
    setEditDescription('');
  };

  const handleDeleteVersion = (versionItem: Version) => {
    if (versionItem.status === 'deployed') {
      alert('无法删除已上线的版本');
      return;
    }

    if (confirm(`确定要删除版本 ${versionItem.version} 吗？`)) {
      setVersions(versions.filter(v => v.id !== versionItem.id));
    }
    setOpenMenuId(null);
  };

  const getStatusColor = (status: Version['status']) => {
    switch (status) {
      case 'deployed': return 'text-green-600 bg-green-100';
      case 'deploying': return 'text-blue-600 bg-blue-100';
      default: return 'text-slate-600 bg-slate-100';
    }
  };

  const getStatusText = (status: Version['status']) => {
    switch (status) {
      case 'deployed': return '已上线';
      case 'deploying': return '部署中';
      default: return '已保存';
    }
  };

  if (!isOpen || !project) return null;

  return (
    <>
      <div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
        onClick={() => setOpenMenuId(null)}
      >
        <div
          className="bg-white rounded-lg shadow-xl w-[1000px] max-h-[85vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* 头部 */}
          <div className="p-6 border-b border-slate-200">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Rocket className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-slate-900">版本发布</h2>
                  <p className="text-sm text-slate-500">{project.name}</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* 说明信息 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <CheckCircle className="w-4 h-4 text-blue-600" />
                </div>
                <div className="text-sm">
                  <h4 className="font-medium text-blue-900 mb-2">版本发布流程</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-blue-800">
                    <div>
                      <p className="font-medium mb-1">第一步：保存版本</p>
                      <ul className="space-y-1 text-blue-700">
                        <li>• 保存当前代码状态为一个版本</li>
                        <li>• 填写版本号和详细描述</li>
                        <li>• 建议在重要功能完成后保存</li>
                      </ul>
                    </div>
                    <div>
                      <p className="font-medium mb-1">第二步：选择发布</p>
                      <ul className="space-y-1 text-blue-700">
                        <li>• 从版本列表中选择要发布的版本</li>
                        <li>• 点击发布按钮部署到生产环境</li>
                        <li>• 同时只能有一个版本处于上线状态</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 内容区域 - 左右布局 */}
          <div className="flex" style={{ height: '450px' }}>
            {/* 左侧：保存新版本 */}
            <div className="w-2/5 p-6 border-r border-slate-200 bg-gradient-to-br from-blue-50 to-indigo-50">
              <div className="flex flex-col">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                    <Save className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-slate-900">保存新版本</h3>
                    <p className="text-sm text-slate-600">将当前代码状态保存为一个版本</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Label htmlFor="version-input" className="text-sm font-medium text-slate-700">
                          版本号 <span className="text-red-500">*</span>
                        </Label>
                        <div className="relative group">
                          <button
                            type="button"
                            className="w-4 h-4 rounded-full bg-slate-200 hover:bg-slate-300 flex items-center justify-center transition-colors"
                          >
                            <Info className="w-2.5 h-2.5 text-slate-600" />
                          </button>
                          {/* 提示框 */}
                          <div className="absolute left-6 top-0 invisible group-hover:visible bg-slate-800 text-white text-xs rounded-lg p-3 whitespace-nowrap z-10 shadow-lg">
                            <div className="space-y-1">
                              <div><strong>主版本</strong>：重大功能更新 (v2.0.0)</div>
                              <div><strong>次版本</strong>：新功能添加 (v1.1.0)</div>
                              <div><strong>修订版</strong>：问题修复 (v1.0.1)</div>
                            </div>
                            {/* 箭头 */}
                            <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-0 h-0 border-t-2 border-b-2 border-r-4 border-transparent border-r-slate-800"></div>
                          </div>
                        </div>
                      </div>
                      <Input
                        id="version-input"
                        value={version}
                        onChange={(e) => setVersion(e.target.value)}
                        placeholder="v1.0.0"
                        className="w-full bg-white border-slate-200 focus:border-blue-500 focus:ring-blue-500 h-10 text-sm"
                      />
                      <p className="text-xs text-slate-500">
                        建议使用语义化版本号，如 v1.0.0
                      </p>
                    </div>

                    <div className="space-y-3">
                      <Label htmlFor="description-input" className="text-sm font-medium text-slate-700">
                        版本描述 <span className="text-red-500">*</span>
                      </Label>
                      <Textarea
                        id="description-input"
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        placeholder="描述本次版本的主要变更和新功能..."
                        rows={4}
                        className="w-full bg-white border-slate-200 focus:border-blue-500 focus:ring-blue-500 resize-none text-sm"
                      />
                      <p className="text-xs text-slate-500">
                        详细描述有助于团队了解版本变更内容
                      </p>
                    </div>
                  </div>

                  <div className="pt-2">
                    <Button
                      onClick={handleSaveVersion}
                      disabled={!version.trim() || !description.trim()}
                      className="w-full h-10 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                    >
                      <Save className="w-4 h-4 mr-2" />
                      保存版本
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* 右侧：版本列表 */}
            <div className="flex-1 flex flex-col min-h-0">
              <div className="flex items-center justify-between p-6 bg-slate-50 border-b border-slate-200">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center">
                    <Rocket className="w-4 h-4 text-slate-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-slate-900">版本列表</h3>
                    <p className="text-sm text-slate-500">选择版本进行发布</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-sm text-slate-500 bg-white px-3 py-1.5 rounded-lg border border-slate-200">
                    共 {versions.length} 个版本
                  </span>
                </div>
              </div>

              <div className="flex-1 overflow-y-auto min-h-0">
                {versions.map((versionItem, index) => (
                  <div
                    key={versionItem.id}
                    className={`p-4 bg-white hover:bg-slate-50 transition-colors ${
                      index !== versions.length - 1 ? 'border-b border-slate-200' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        {editingVersion?.id === versionItem.id ? (
                          <div className="space-y-2">
                            <Input
                              value={editDescription}
                              onChange={(e) => setEditDescription(e.target.value)}
                              className="text-sm"
                              placeholder="版本描述"
                            />
                            <div className="flex items-center space-x-2">
                              <Button
                                size="sm"
                                onClick={handleSaveEdit}
                                className="h-6 px-2 text-xs"
                              >
                                保存
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={handleCancelEdit}
                                className="h-6 px-2 text-xs"
                              >
                                取消
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <>
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex items-center space-x-3">
                                <h4 className="text-lg font-semibold text-slate-900">{versionItem.version}</h4>
                                <span className={`text-xs px-2.5 py-1 rounded-full font-medium ${getStatusColor(versionItem.status)}`}>
                                  {getStatusText(versionItem.status)}
                                </span>
                              </div>
                              <div className="flex items-center space-x-2">
                                {versionItem.status === 'saved' && (
                                  <Button
                                    size="sm"
                                    onClick={() => handleDeploy(versionItem)}
                                    disabled={isDeploying}
                                    className="h-8 px-3 text-sm bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors"
                                  >
                                    <Rocket className="w-4 h-4 mr-1.5" />
                                    发布上线
                                  </Button>
                                )}
                                {versionItem.status === 'deploying' && (
                                  <div className="flex items-center space-x-2 text-sm text-blue-600 bg-blue-50 px-3 py-1.5 rounded-lg">
                                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                                    <span className="font-medium">部署中</span>
                                  </div>
                                )}
                                {versionItem.status === 'deployed' && (
                                  <div className="flex items-center space-x-2 text-sm text-green-600 bg-green-50 px-3 py-1.5 rounded-lg">
                                    <CheckCircle className="w-4 h-4" />
                                    <span className="font-medium">已上线</span>
                                  </div>
                                )}
                              </div>
                            </div>
                            <p className="text-sm text-slate-600 mb-3 leading-relaxed">{versionItem.description}</p>
                            <div className="flex items-center text-xs text-slate-500">
                              <Clock className="w-3.5 h-3.5 mr-1.5" />
                              <span>创建于 {versionItem.createdAt}</span>
                            </div>
                          </>
                        )}
                      </div>

                      {editingVersion?.id !== versionItem.id && (
                        <div className="relative ml-2">
                          <button
                            onClick={() => setOpenMenuId(openMenuId === versionItem.id ? null : versionItem.id)}
                            className="p-1 text-slate-400 hover:text-slate-600 rounded"
                          >
                            <MoreVertical className="w-3 h-3" />
                          </button>

                          {openMenuId === versionItem.id && (
                            <div className="absolute right-0 top-6 bg-white border border-slate-200 rounded-lg shadow-lg z-10 py-1 min-w-[80px]">
                              <button
                                onClick={() => handleEditVersion(versionItem)}
                                className="w-full px-3 py-1.5 text-xs text-left hover:bg-slate-50 flex items-center space-x-1"
                              >
                                <Edit className="w-3 h-3" />
                                <span>编辑</span>
                              </button>
                              {versionItem.status !== 'deployed' && (
                                <button
                                  onClick={() => handleDeleteVersion(versionItem)}
                                  className="w-full px-3 py-1.5 text-xs text-left hover:bg-slate-50 text-red-600 flex items-center space-x-1"
                                >
                                  <Trash2 className="w-3 h-3" />
                                  <span>删除</span>
                                </button>
                              )}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {versions.length === 0 && (
                  <div className="text-center py-12 text-slate-500">
                    <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <AlertCircle className="w-8 h-8 text-slate-400" />
                    </div>
                    <h4 className="text-base font-medium text-slate-700 mb-2">暂无版本</h4>
                    <p className="text-sm text-slate-500">请先在上方保存一个版本，然后就可以选择发布了</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 底部信息 */}
          <div className="px-6 py-4 border-t border-slate-200 bg-gradient-to-r from-slate-50 to-slate-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 text-sm text-slate-600">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>当前上线版本: <span className="font-medium text-slate-800">{versions.find(v => v.status === 'deployed')?.version || '无'}</span></span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="w-3.5 h-3.5 text-slate-400" />
                  <span>最后更新: <span className="font-medium text-slate-800">{versions[0]?.createdAt || '无'}</span></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default UnifiedDeployModal;
