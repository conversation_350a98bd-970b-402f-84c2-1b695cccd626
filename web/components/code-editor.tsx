'use client'

import React, { useState, useEffect, useRef } from 'react';
import Editor from '@monaco-editor/react';
import { Code, FileText, Folder, FolderOpen, ChevronRight, ChevronDown } from 'lucide-react';

export interface FileNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  children?: FileNode[];
  content?: string;
  language?: string;
}

interface CodeEditorProps {
  file: string;
  onChange: (content: string) => void;
  fileTree?: FileNode[];
  onFileSelect?: (fileId: string) => void;
  showFileExplorer?: boolean;
}

const CodeEditor: React.FC<CodeEditorProps> = ({
  file,
  onChange,
  fileTree = [],
  onFileSelect,
  showFileExplorer = false
}) => {
  const [content, setContent] = useState('');

  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['app', 'components']));
  const [currentFile, setCurrentFile] = useState<string>(file);
  const editorRef = useRef<any>(null);

  useEffect(() => {
    setCurrentFile(file);
    // 模拟加载文件内容
    const mockContent = `import React from 'react';
import { useState } from 'react';

const App: React.FC = () => {
  const [count, setCount] = useState(0);

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">
          欢迎使用 AI Web IDE
        </h1>
        <p className="text-gray-600 mb-4">
          计数器值: {count}
        </p>
        <button
          onClick={() => setCount(count + 1)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
        >
          点击增加
        </button>
      </div>
    </div>
  );
};

export default App;`;
    setContent(mockContent);
  }, [file]);

  const handleContentChange = (value: string | undefined) => {
    const newContent = value || '';
    setContent(newContent);
    onChange(newContent);
  };

  const getFileIcon = (filename: string) => {
    if (filename.endsWith('.tsx') || filename.endsWith('.jsx')) {
      return <Code className="w-4 h-4 text-blue-400" />;
    }
    return <FileText className="w-4 h-4 text-gray-400" />;
  };

  const getLanguage = (filename: string) => {
    if (filename.endsWith('.tsx') || filename.endsWith('.jsx')) {
      return 'typescript';
    } else if (filename.endsWith('.js')) {
      return 'javascript';
    } else if (filename.endsWith('.css')) {
      return 'css';
    } else if (filename.endsWith('.html')) {
      return 'html';
    } else if (filename.endsWith('.json')) {
      return 'json';
    } else if (filename.endsWith('.md')) {
      return 'markdown';
    }
    return 'typescript';
  };



  const toggleFolder = (folderId: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderId)) {
      newExpanded.delete(folderId);
    } else {
      newExpanded.add(folderId);
    }
    setExpandedFolders(newExpanded);
  };

  const handleFileClick = (fileId: string) => {
    setCurrentFile(fileId);
    onFileSelect?.(fileId);
  };

  const renderFileTree = (nodes: FileNode[], level = 0): React.ReactNode => {
    return nodes.map((node) => (
      <div key={node.id} style={{ paddingLeft: `${level * 16}px` }}>
        <div
          className={`flex items-center space-x-1 px-2 py-1 text-sm cursor-pointer hover:bg-slate-200 rounded ${
            currentFile === node.id ? 'bg-blue-100 text-blue-700' : 'text-slate-700'
          }`}
          onClick={() => {
            if (node.type === 'folder') {
              toggleFolder(node.id);
            } else {
              handleFileClick(node.id);
            }
          }}
        >
          {node.type === 'folder' ? (
            <>
              {expandedFolders.has(node.id) ? (
                <ChevronDown className="w-3 h-3" />
              ) : (
                <ChevronRight className="w-3 h-3" />
              )}
              {expandedFolders.has(node.id) ? (
                <FolderOpen className="w-4 h-4 text-blue-500" />
              ) : (
                <Folder className="w-4 h-4 text-blue-500" />
              )}
            </>
          ) : (
            <>
              <div className="w-3 h-3" />
              {getFileIcon(node.name)}
            </>
          )}
          <span>{node.name}</span>
        </div>
        {node.type === 'folder' && expandedFolders.has(node.id) && node.children && (
          <div>
            {renderFileTree(node.children, level + 1)}
          </div>
        )}
      </div>
    ));
  };

  return (
    <div className="h-full flex bg-white">
      {/* 文件浏览器 */}
      {showFileExplorer && (
        <div className="w-64 bg-slate-50 border-r border-slate-200 flex flex-col">
          <div className="p-3 border-b border-slate-200">
            <h3 className="text-sm font-medium text-slate-900">文件浏览器</h3>
          </div>
          <div className="flex-1 overflow-y-auto p-2">
            {renderFileTree(fileTree)}
          </div>
        </div>
      )}

      {/* 编辑器区域 */}
      <div className="flex-1 flex flex-col">


        {/* 编辑器头部 */}
        <div className="flex items-center p-3 border-b border-slate-200 bg-white h-12 flex-shrink-0">
          <div className="flex items-center space-x-2">
            {getFileIcon(currentFile)}
            <span className="text-sm font-medium text-slate-700">{currentFile}</span>
          </div>
        </div>

        {/* 编辑器内容 */}
        <div className="flex-1 relative">
          <Editor
            height="100%"
            language={getLanguage(currentFile)}
            value={content}
            onChange={handleContentChange}
            theme="vs-light"
            loading={<div className="flex items-center justify-center h-full text-slate-400">加载编辑器...</div>}
            onMount={(editor) => {
              editorRef.current = editor;
            }}
            options={{
              minimap: { enabled: false },
              fontSize: 13,
              lineNumbers: 'on',
              roundedSelection: false,
              scrollBeyondLastLine: false,
              automaticLayout: true,
              tabSize: 2,
              insertSpaces: true,
              wordWrap: 'on',
              contextmenu: true,
              selectOnLineNumbers: true,
              glyphMargin: false,
              folding: true,
              lineDecorationsWidth: 10,
              lineNumbersMinChars: 3,
              renderLineHighlight: 'line',
              scrollbar: {
                vertical: 'auto',
                horizontal: 'auto',
                verticalScrollbarSize: 10,
                horizontalScrollbarSize: 10
              }
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default CodeEditor;
