'use client'

import React, { useEffect } from 'react';

interface GoogleLoginButtonProps {
  onSuccess: (credential: string) => void;
  onError?: (error: any) => void;
  disabled?: boolean;
  text?: string;
}

declare global {
  interface Window {
    google?: any;
    handleCredentialResponse?: (response: any) => void;
  }
}

const GoogleLoginButton: React.FC<GoogleLoginButtonProps> = ({
  onSuccess,
  onError,
  disabled = false,
  text = '使用 Google 登录'
}) => {
  useEffect(() => {
    // 定义全局回调函数
    window.handleCredentialResponse = (response: any) => {
      if (response.credential) {
        onSuccess(response.credential);
      } else {
        onError?.(new Error('No credential received'));
      }
    };

    // 加载 Google Identity Services 脚本
    const script = document.createElement('script');
    script.src = 'https://accounts.google.com/gsi/client';
    script.async = true;
    script.defer = true;
    
    script.onload = () => {
      if (window.google) {
        window.google.accounts.id.initialize({
          client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
          callback: window.handleCredentialResponse,
        });

        window.google.accounts.id.renderButton(
          document.getElementById('google-signin-button'),
          {
            theme: 'outline',
            size: 'large',
            text: 'signin_with',
            shape: 'rectangular',
            logo_alignment: 'left',
            width: '100%',
          }
        );
      }
    };

    script.onerror = () => {
      onError?.(new Error('Failed to load Google Identity Services'));
    };

    document.head.appendChild(script);

    return () => {
      // 清理
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
      delete window.handleCredentialResponse;
    };
  }, [onSuccess, onError]);

  return (
    <div className="w-full">
      <div 
        id="google-signin-button" 
        className={`w-full ${disabled ? 'opacity-50 pointer-events-none' : ''}`}
      />
      {!process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID && (
        <div className="mt-2 text-sm text-red-600">
          Google Client ID not configured
        </div>
      )}
    </div>
  );
};

export default GoogleLoginButton;
