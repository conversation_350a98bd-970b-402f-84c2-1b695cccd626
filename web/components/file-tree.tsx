'use client'

import React, { useState } from 'react';
import { 
  ChevronDown, 
  ChevronRight, 
  File, 
  Folder, 
  FolderOpen,
  Plus,
  MoreHorizontal,
  Edit3,
  Trash2,
  Copy
} from 'lucide-react';

interface FileNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  children?: FileNode[];
  content?: string;
  language?: string;
}

interface FileTreeProps {
  files: FileNode[];
  selectedFile: string;
  onFileSelect: (fileId: string) => void;
  expandedFolders: Set<string>;
  onToggleFolder: (folderId: string) => void;
  onCreateFile?: (parentId: string, name: string) => void;
  onCreateFolder?: (parentId: string, name: string) => void;
  onRenameFile?: (fileId: string, newName: string) => void;
  onDeleteFile?: (fileId: string) => void;
  className?: string;
}

export default function FileTree({
  files,
  selectedFile,
  onFileSelect,
  expandedFolders,
  onToggleFolder,
  onCreateFile,
  onCreateFolder,
  onRenameFile,
  onDeleteFile,
  className = ''
}: FileTreeProps) {
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    fileId: string;
    type: 'file' | 'folder';
  } | null>(null);
  const [editingFile, setEditingFile] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');




  // 模拟文件树结构
  const mockFiles: FileNode[] = [
    {
      id: 'src',
      name: 'src',
      type: 'folder',
      children: [
        { id: 'src/App.tsx', name: 'App.tsx', type: 'file' },
        { id: 'src/index.css', name: 'index.css', type: 'file' },
        { id: 'src/main.tsx', name: 'main.tsx', type: 'file' },
        {
          id: 'src/components',
          name: 'components',
          type: 'folder',
          children: [
            { id: 'src/components/Header.tsx', name: 'Header.tsx', type: 'file' },
            { id: 'src/components/Footer.tsx', name: 'Footer.tsx', type: 'file' },
          ]
        },
      ]
    },
    { id: 'package.json', name: 'package.json', type: 'file' },
    { id: 'tsconfig.json', name: 'tsconfig.json', type: 'file' },
    { id: 'vite.config.ts', name: 'vite.config.ts', type: 'file' },
  ];

  const getFileIcon = (fileName: string) => {
    if (fileName.endsWith('.tsx') || fileName.endsWith('.jsx')) {
      return <File className="w-4 h-4 text-blue-400" />;
    }
    if (fileName.endsWith('.css')) {
      return <File className="w-4 h-4 text-green-400" />;
    }
    if (fileName.endsWith('.json')) {
      return <File className="w-4 h-4 text-yellow-400" />;
    }
    return <File className="w-4 h-4 text-gray-400" />;
  };

  const renderFileNode = (node: FileNode, level: number = 0): React.ReactNode => {
    const isExpanded = expandedFolders.has(node.id);
    const isSelected = selectedFile === node.id;

    return (
      <div key={node.id}>
        <div
          className={`flex items-center space-x-2 px-2 py-1 hover:bg-gray-700 cursor-pointer rounded ${
            isSelected ? 'bg-blue-600/20 text-blue-400' : 'text-gray-300'
          }`}
          style={{ paddingLeft: `${(level + 1) * 12}px` }}
          onClick={() => node.type === 'file' ? onFileSelect(node.id) : onToggleFolder(node.id)}
        >
          {node.type === 'folder' && (
            <button className="text-gray-400 hover:text-white">
              {isExpanded ? <ChevronDown className="w-3 h-3" /> : <ChevronRight className="w-3 h-3" />}
            </button>
          )}
          {node.type === 'folder' ? (
            <Folder className="w-4 h-4 text-blue-400" />
          ) : (
            getFileIcon(node.name)
          )}
          <span className="text-sm truncate">{node.name}</span>
        </div>
        {node.type === 'folder' && isExpanded && node.children && (
          <div>
            {node.children.map(child => renderFileNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* 搜索框 */}
      <div className="p-3 border-b border-gray-700">
        <div className="relative">
          <Plus className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3" />
          <input
            type="text"
            placeholder="搜索文件..."
            className="w-full pl-8 pr-3 py-1 bg-gray-700 border border-gray-600 rounded text-xs focus:outline-none focus:ring-1 focus:ring-blue-500 text-gray-300"
          />
        </div>
      </div>

      {/* 文件树 */}
      <div className="flex-1 overflow-y-auto p-2">
        {mockFiles.map(file => renderFileNode(file))}
      </div>
    </div>
  );
};
