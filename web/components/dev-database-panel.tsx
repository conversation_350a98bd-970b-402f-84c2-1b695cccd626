'use client'

import React, { useState } from 'react';
import { X, Database, Play, RefreshCw, CheckCircle, TrendingUp, FileText, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface DevDatabasePanelProps {
  isOpen: boolean;
  onClose: () => void;
}

const DevDatabasePanel: React.FC<DevDatabasePanelProps> = ({ isOpen, onClose }) => {
  const [selectedTable, setSelectedTable] = useState('users');
  const [sqlQuery, setSqlQuery] = useState('SELECT * FROM users LIMIT 10;');
  const [queryResult, setQueryResult] = useState<any[]>([]);
  const [isExecutingQuery, setIsExecutingQuery] = useState(false);
  const [databaseView, setDatabaseView] = useState<'overview' | 'query' | 'tables'>('overview');

  // 模拟开发环境数据库表数据
  const tableData = {
    users: [
      { id: 1, username: 'dev_user1', email: '<EMAIL>', created_at: '2024-01-10 09:30:00', status: 'active' },
      { id: 2, username: 'dev_user2', email: '<EMAIL>', created_at: '2024-01-11 14:20:00', status: 'active' },
      { id: 3, username: 'test_user', email: '<EMAIL>', created_at: '2024-01-12 16:45:00', status: 'inactive' }
    ],
    projects: [
      { id: 1, name: 'Test Project', description: '测试项目', owner_id: 1, created_at: '2024-01-10 10:00:00', status: 'development' },
      { id: 2, name: 'Demo App', description: '演示应用', owner_id: 2, created_at: '2024-01-11 15:30:00', status: 'development' }
    ],
    logs: [
      { id: 1, level: 'info', message: '应用启动成功', created_at: '2024-01-12 09:00:00' },
      { id: 2, level: 'error', message: '数据库连接失败', created_at: '2024-01-12 09:05:00' }
    ]
  };

  const executeQuery = async () => {
    setIsExecutingQuery(true);
    // 模拟查询执行
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // 简单的查询解析和结果模拟
    const query = sqlQuery.toLowerCase().trim();
    if (query.includes('select') && query.includes('from')) {
      if (query.includes('users')) {
        setQueryResult(tableData.users);
      } else if (query.includes('projects')) {
        setQueryResult(tableData.projects);
      } else if (query.includes('logs')) {
        setQueryResult(tableData.logs);
      } else {
        setQueryResult([]);
      }
    } else {
      setQueryResult([{ message: '查询执行成功', affected_rows: Math.floor(Math.random() * 5) + 1 }]);
    }
    
    setIsExecutingQuery(false);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-[90vw] h-[85vh] max-w-6xl flex flex-col">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-slate-200">
          <div className="flex items-center space-x-3">
            <h2 className="text-lg font-semibold text-slate-800">开发环境数据库</h2>
            <span className="flex items-center space-x-1 text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
              <CheckCircle className="w-3 h-3" />
              <span>SQLite 本地开发</span>
            </span>
            <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded font-medium">DEV</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setDatabaseView('overview')}
                className={`px-3 py-1 text-xs rounded transition-colors ${
                  databaseView === 'overview' 
                    ? 'bg-green-100 text-green-700' 
                    : 'text-slate-600 hover:bg-slate-100'
                }`}
              >
                概览
              </button>
              <button
                onClick={() => setDatabaseView('query')}
                className={`px-3 py-1 text-xs rounded transition-colors ${
                  databaseView === 'query' 
                    ? 'bg-green-100 text-green-700' 
                    : 'text-slate-600 hover:bg-slate-100'
                }`}
              >
                SQL查询
              </button>
              <button
                onClick={() => setDatabaseView('tables')}
                className={`px-3 py-1 text-xs rounded transition-colors ${
                  databaseView === 'tables' 
                    ? 'bg-green-100 text-green-700' 
                    : 'text-slate-600 hover:bg-slate-100'
                }`}
              >
                表管理
              </button>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-hidden p-4">
          {databaseView === 'overview' && (
            <div>
              {/* 数据库状态卡片 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div className="bg-green-50 rounded-lg p-4 border border-green-100">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-green-800">数据库状态</h4>
                    <Database className="w-4 h-4 text-green-600" />
                  </div>
                  <div className="space-y-2 text-xs">
                    <div className="flex justify-between">
                      <span className="text-green-600">类型:</span>
                      <span className="text-green-800 font-medium">SQLite</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-green-600">文件大小:</span>
                      <span className="text-green-800 font-medium">2.1 MB</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-green-600">连接状态:</span>
                      <span className="text-green-800 font-medium">已连接</span>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-4 border border-green-100">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-green-800">表统计</h4>
                    <TrendingUp className="w-4 h-4 text-green-600" />
                  </div>
                  <div className="space-y-2 text-xs">
                    <div className="flex justify-between">
                      <span className="text-green-600">总表数:</span>
                      <span className="text-green-800 font-medium">3</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-green-600">总记录数:</span>
                      <span className="text-green-800 font-medium">7</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-green-600">最后更新:</span>
                      <span className="text-green-800 font-medium">刚刚</span>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-4 border border-green-100">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-green-800">查询统计</h4>
                    <FileText className="w-4 h-4 text-green-600" />
                  </div>
                  <div className="space-y-2 text-xs">
                    <div className="flex justify-between">
                      <span className="text-green-600">今日查询:</span>
                      <span className="text-green-800 font-medium">24</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-green-600">平均响应:</span>
                      <span className="text-green-800 font-medium">12ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-green-600">错误率:</span>
                      <span className="text-green-800 font-medium">0%</span>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-4 border border-green-100">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-green-800">开发工具</h4>
                    <Settings className="w-4 h-4 text-green-600" />
                  </div>
                  <div className="space-y-2 text-xs">
                    <div className="flex justify-between">
                      <span className="text-green-600">自动迁移:</span>
                      <span className="text-green-800 font-medium">已启用</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-green-600">种子数据:</span>
                      <span className="text-green-800 font-medium">已加载</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-green-600">调试模式:</span>
                      <span className="text-green-800 font-medium">开启</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 表列表 */}
              <div className="bg-white border border-green-200 rounded-lg">
                <div className="p-4 border-b border-green-200 bg-green-50">
                  <h4 className="text-sm font-medium text-green-800">开发数据表</h4>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full text-xs">
                    <thead className="bg-green-50">
                      <tr>
                        <th className="text-left py-2 px-4 font-medium text-green-600">表名</th>
                        <th className="text-left py-2 px-4 font-medium text-green-600">记录数</th>
                        <th className="text-left py-2 px-4 font-medium text-green-600">大小</th>
                        <th className="text-left py-2 px-4 font-medium text-green-600">最后更新</th>
                        <th className="text-left py-2 px-4 font-medium text-green-600">操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b border-green-100">
                        <td className="py-3 px-4 font-medium text-slate-800">users</td>
                        <td className="py-3 px-4 text-slate-600">3</td>
                        <td className="py-3 px-4 text-slate-600">1.2 KB</td>
                        <td className="py-3 px-4 text-slate-600">刚刚</td>
                        <td className="py-3 px-4">
                          <button 
                            onClick={() => {
                              setSelectedTable('users');
                              setSqlQuery('SELECT * FROM users LIMIT 10;');
                              setDatabaseView('query');
                            }}
                            className="text-green-600 hover:text-green-800 text-xs"
                          >
                            查询
                          </button>
                        </td>
                      </tr>
                      <tr className="border-b border-green-100">
                        <td className="py-3 px-4 font-medium text-slate-800">projects</td>
                        <td className="py-3 px-4 text-slate-600">2</td>
                        <td className="py-3 px-4 text-slate-600">0.8 KB</td>
                        <td className="py-3 px-4 text-slate-600">1分钟前</td>
                        <td className="py-3 px-4">
                          <button 
                            onClick={() => {
                              setSelectedTable('projects');
                              setSqlQuery('SELECT * FROM projects LIMIT 10;');
                              setDatabaseView('query');
                            }}
                            className="text-green-600 hover:text-green-800 text-xs"
                          >
                            查询
                          </button>
                        </td>
                      </tr>
                      <tr className="border-b border-green-100">
                        <td className="py-3 px-4 font-medium text-slate-800">logs</td>
                        <td className="py-3 px-4 text-slate-600">2</td>
                        <td className="py-3 px-4 text-slate-600">0.5 KB</td>
                        <td className="py-3 px-4 text-slate-600">30秒前</td>
                        <td className="py-3 px-4">
                          <button 
                            onClick={() => {
                              setSelectedTable('logs');
                              setSqlQuery('SELECT * FROM logs LIMIT 10;');
                              setDatabaseView('query');
                            }}
                            className="text-green-600 hover:text-green-800 text-xs"
                          >
                            查询
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {databaseView === 'query' && (
            <div className="h-full flex flex-col">
              {/* SQL查询工具栏 */}
              <div className="flex items-center justify-between mb-4 p-3 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-center space-x-3">
                  <select
                    value={selectedTable}
                    onChange={(e) => {
                      setSelectedTable(e.target.value);
                      setSqlQuery(`SELECT * FROM ${e.target.value} LIMIT 10;`);
                    }}
                    className="text-xs border border-green-200 rounded px-2 py-1 text-green-700 bg-white"
                  >
                    <option value="users">users</option>
                    <option value="projects">projects</option>
                    <option value="logs">logs</option>
                  </select>
                  <button
                    onClick={executeQuery}
                    disabled={isExecutingQuery}
                    className="flex items-center space-x-1 px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isExecutingQuery ? (
                      <>
                        <RefreshCw className="w-3 h-3 animate-spin" />
                        <span>执行中...</span>
                      </>
                    ) : (
                      <>
                        <Play className="w-3 h-3" />
                        <span>执行查询</span>
                      </>
                    )}
                  </button>
                </div>
                <div className="text-xs text-green-600">
                  {queryResult.length > 0 && `${queryResult.length} 条记录`}
                </div>
              </div>

              {/* SQL编辑器 */}
              <div className="mb-4 p-1">
                <textarea
                  value={sqlQuery}
                  onChange={(e) => setSqlQuery(e.target.value)}
                  className="w-full h-24 text-xs font-mono border border-green-200 rounded p-3 resize-none focus:outline-none focus:ring-0 focus:border-green-500 text-slate-800 bg-white shadow-sm"
                  placeholder="输入 SQL 查询语句..."
                  style={{ boxShadow: 'none' }}
                />
              </div>

              {/* 查询结果 */}
              <div className="flex-1 overflow-hidden">
                <div className="h-full overflow-auto border border-green-200 rounded-lg bg-white">
                  {queryResult.length > 0 ? (
                    <table className="w-full text-xs">
                      <thead className="bg-green-50 sticky top-0">
                        <tr>
                          {Object.keys(queryResult[0]).map((key) => (
                            <th key={key} className="text-left py-2 px-3 font-medium text-green-600 border-b border-green-200">
                              {key}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="text-slate-700">
                        {queryResult.map((row, index) => (
                          <tr key={index} className="border-b border-green-100 hover:bg-green-50">
                            {Object.values(row).map((value, cellIndex) => (
                              <td key={cellIndex} className="py-2 px-3">
                                {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  ) : (
                    <div className="flex items-center justify-center h-32 text-slate-500">
                      <div className="text-center">
                        <Database className="w-8 h-8 mx-auto mb-2 text-green-400" />
                        <p className="text-sm">执行查询以查看结果</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {databaseView === 'tables' && (
            <div>
              <div className="bg-white border border-green-200 rounded-lg">
                <div className="p-4 border-b border-green-200 bg-green-50">
                  <h4 className="text-sm font-medium text-green-800">开发环境表结构管理</h4>
                </div>
                <div className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {Object.keys(tableData).map((tableName) => (
                      <div key={tableName} className="border border-green-200 rounded-lg p-3 bg-green-50">
                        <div className="flex items-center justify-between mb-2">
                          <h5 className="text-sm font-medium text-green-800">{tableName}</h5>
                          <span className="text-xs text-green-600">
                            {tableData[tableName as keyof typeof tableData].length} 条记录
                          </span>
                        </div>
                        <div className="space-y-1 text-xs text-green-600">
                          {Object.keys(tableData[tableName as keyof typeof tableData][0] || {}).map((column) => (
                            <div key={column} className="flex justify-between">
                              <span>{column}</span>
                              <span className="text-green-400">
                                {typeof (tableData[tableName as keyof typeof tableData][0] as any)?.[column] === 'number' ? 'INT' : 'TEXT'}
                              </span>
                            </div>
                          ))}
                        </div>
                        <div className="mt-3 flex space-x-2">
                          <button
                            onClick={() => {
                              setSelectedTable(tableName);
                              setSqlQuery(`SELECT * FROM ${tableName} LIMIT 10;`);
                              setDatabaseView('query');
                            }}
                            className="text-xs text-green-600 hover:text-green-800"
                          >
                            查询数据
                          </button>
                          <button className="text-xs text-green-600 hover:text-green-800">
                            查看结构
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DevDatabasePanel;
