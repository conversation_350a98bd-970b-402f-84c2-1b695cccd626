'use client'

import React, { useState } from 'react';
import { Eye, Code, Terminal, X, ChevronUp, ChevronDown } from 'lucide-react';
import CodeEditor, { FileNode } from './code-editor';
import PreviewPanel from './preview-panel';

interface Project {
  id: string;
  name: string;
  status: 'running' | 'stopped' | 'error';
  framework: string;
  createdAt: string;
}

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warn' | 'error';
  message: string;
  source: 'frontend' | 'backend';
}

interface MainWorkspaceProps {
  currentProject?: Project;
}

const MainWorkspace: React.FC<MainWorkspaceProps> = ({ currentProject }) => {
  const [activeTab, setActiveTab] = useState<'code' | 'preview'>('code');
  const [selectedFile, setSelectedFile] = useState<string>('app/App.tsx');
  const [fileContent, setFileContent] = useState<string>('');
  const [consoleVisible, setConsoleVisible] = useState<boolean>(false);
  const [consoleHeight, setConsoleHeight] = useState<number>(200);
  
  const frontendLogs: LogEntry[] = [
    {
      id: '1',
      timestamp: new Date(Date.now() - 5000).toISOString(),
      level: 'info',
      message: 'React application started',
      source: 'frontend'
    },
    {
      id: '2',
      timestamp: new Date(Date.now() - 3000).toISOString(),
      level: 'warn',
      message: 'Component re-rendered 3 times',
      source: 'frontend'
    },
    {
      id: '3',
      timestamp: new Date().toISOString(),
      level: 'info',
      message: 'API call successful: GET /api/projects',
      source: 'frontend'
    }
  ];

  const backendLogs: LogEntry[] = [
    {
      id: '4',
      timestamp: new Date(Date.now() - 10000).toISOString(),
      level: 'info',
      message: 'Server starting...',
      source: 'backend'
    },
    {
      id: '5',
      timestamp: new Date(Date.now() - 8000).toISOString(),
      level: 'info',
      message: 'Database connected successfully',
      source: 'backend'
    },
    {
      id: '6',
      timestamp: new Date(Date.now() - 6000).toISOString(),
      level: 'info',
      message: 'Server listening on port 3000',
      source: 'backend'
    },
    {
      id: '7',
      timestamp: new Date(Date.now() - 2000).toISOString(),
      level: 'info',
      message: 'GET /api/projects - 200 OK (45ms)',
      source: 'backend'
    }
  ];

  const fileTree: FileNode[] = [
    {
      id: 'app',
      name: 'app',
      type: 'folder',
      children: [
        { id: 'app/App.tsx', name: 'App.tsx', type: 'file', language: 'typescript' },
        { id: 'app/index.css', name: 'index.css', type: 'file', language: 'css' }
      ]
    },
    {
      id: 'components',
      name: 'components',
      type: 'folder',
      children: [
        { id: 'components/Button.tsx', name: 'Button.tsx', type: 'file', language: 'typescript' }
      ]
    }
  ];

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'text-red-600';
      case 'warn': return 'text-yellow-600';
      case 'info': return 'text-blue-600';
      default: return 'text-slate-600';
    }
  };

  return (
    <div className="h-full flex flex-col bg-white">
      <div className="bg-white border-b border-slate-200 flex-shrink-0">
        <div className="flex h-12">
          <button
            onClick={() => setActiveTab('code')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'code'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-slate-500 hover:text-slate-700'
            }`}
          >
            <Code className="w-4 h-4 inline mr-2" />
            代码编辑
          </button>
          <button
            onClick={() => setActiveTab('preview')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'preview'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-slate-500 hover:text-slate-700'
            }`}
          >
            <Eye className="w-4 h-4 inline mr-2" />
            预览
          </button>

        </div>
      </div>

      <div className="flex-1 relative overflow-hidden border-b border-slate-200">
        {activeTab === 'code' ? (
          <CodeEditor
            file={selectedFile || 'app/App.tsx'}
            onChange={(content) => setFileContent(content)}
            fileTree={fileTree}
            onFileSelect={setSelectedFile}
            showFileExplorer={true}
          />
        ) : (
          <PreviewPanel
            projectId={currentProject?.id || ''}
            isRunning={currentProject?.status === 'running'}
          />
        )}
      </div>

      <div className="flex-shrink-0">
        {!consoleVisible && (
          <div
            onClick={() => setConsoleVisible(true)}
            className="bg-slate-50 border-t border-slate-200 px-4 py-2 flex items-center justify-between cursor-pointer hover:bg-slate-100 transition-colors"
          >
            <div className="flex items-center space-x-2 text-xs text-slate-600">
              <Terminal className="w-3 h-3" />
              <span>Console</span>
              <ChevronUp className="w-3 h-3" />
            </div>
          </div>
        )}

        {consoleVisible && (
          <div
            className="bg-white border-t border-slate-200 flex flex-col"
            style={{ height: `${consoleHeight}px` }}
          >
            <div
              onClick={() => setConsoleVisible(false)}
              className="flex items-center justify-between bg-slate-50 border-b border-slate-200 px-3 py-2 cursor-pointer hover:bg-slate-100 transition-colors flex-shrink-0"
              title="点击关闭控制台"
            >
              <div className="flex items-center space-x-2">
                <span className="text-xs font-medium text-slate-700">Console</span>
                <ChevronDown className="w-3 h-3 text-slate-500" />
              </div>
              <div className="flex items-center space-x-1 text-xs text-slate-500">
                <span>点击关闭</span>
                <X className="w-3 h-3" />
              </div>
            </div>

            {/* 左右分栏的控制台内容 */}
            <div className="flex-1 flex">
              {/* 后端日志 */}
              <div className="flex-1 flex flex-col border-r border-slate-200">
                <div className="bg-slate-100 px-3 py-2 border-b border-slate-200">
                  <span className="text-xs font-medium text-slate-700">后端日志</span>
                  <span className="text-xs text-slate-500 ml-2">(服务器运行日志)</span>
                </div>
                <div className="flex-1 overflow-y-auto p-3 text-sm font-mono bg-white">
                  {backendLogs.length === 0 ? (
                    <div className="text-slate-400">暂无后端日志</div>
                  ) : (
                    <div className="space-y-1">
                      {backendLogs.map((log) => (
                        <div key={log.id} className="flex items-start space-x-2">
                          <span className="text-slate-400 text-xs">
                            {new Date(log.timestamp).toLocaleTimeString()}
                          </span>
                          <span className={`text-xs font-medium ${getLogLevelColor(log.level)}`}>
                            [{log.level.toUpperCase()}]
                          </span>
                          <span className="text-slate-700">{log.message}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* 前端日志 */}
              <div className="flex-1 flex flex-col">
                <div className="bg-slate-100 px-3 py-2 border-b border-slate-200">
                  <span className="text-xs font-medium text-slate-700">前端日志</span>
                  <span className="text-xs text-slate-500 ml-2">(浏览器控制台日志)</span>
                </div>
                <div className="flex-1 overflow-y-auto p-3 text-sm font-mono bg-white">
                  {frontendLogs.length === 0 ? (
                    <div className="text-slate-400">暂无前端日志</div>
                  ) : (
                    <div className="space-y-1">
                      {frontendLogs.map((log) => (
                        <div key={log.id} className="flex items-start space-x-2">
                          <span className="text-slate-400 text-xs">
                            {new Date(log.timestamp).toLocaleTimeString()}
                          </span>
                          <span className={`text-xs font-medium ${getLogLevelColor(log.level)}`}>
                            [{log.level.toUpperCase()}]
                          </span>
                          <span className="text-slate-700">{log.message}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MainWorkspace;
