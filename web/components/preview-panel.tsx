'use client'

import React, { useState, useRef, useEffect } from 'react';
import { 
  RefreshCw, 
  ExternalLink, 
  Smartphone, 
  Tablet, 
  Monitor, 
  RotateCcw,
  Maximize2,
  Settings
} from 'lucide-react';

interface PreviewPanelProps {
  projectId: string;
  isRunning: boolean;
  className?: string;
}

type DeviceType = 'mobile' | 'tablet' | 'desktop';

interface Device {
  type: DeviceType;
  name: string;
  width: number;
  height: number;
  icon: React.ReactNode;
}

const devices: Device[] = [
  {
    type: 'mobile',
    name: 'iPhone',
    width: 375,
    height: 667,
    icon: <Smartphone className="w-4 h-4" />
  },
  {
    type: 'tablet',
    name: 'iPad',
    width: 768,
    height: 1024,
    icon: <Tablet className="w-4 h-4" />
  },
  {
    type: 'desktop',
    name: 'Desktop',
    width: 1200,
    height: 800,
    icon: <Monitor className="w-4 h-4" />
  }
];

function PreviewPanel({
  projectId,
  isRunning,
  className = ''
}: PreviewPanelProps) {
  const [selectedDevice, setSelectedDevice] = useState<DeviceType>('desktop');
  const [isLoading, setIsLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [scale, setScale] = useState(1);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 模拟预览URL
    setPreviewUrl(`http://localhost:3000/preview/${projectId}`);
  }, [projectId]);

  useEffect(() => {
    // 计算缩放比例以适应容器
    if (containerRef.current) {
      const container = containerRef.current;
      const device = devices.find(d => d.type === selectedDevice);
      if (device) {
        const containerWidth = container.clientWidth - 40; // 减去padding
        const containerHeight = container.clientHeight - 40;
        const scaleX = containerWidth / device.width;
        const scaleY = containerHeight / device.height;
        setScale(Math.min(scaleX, scaleY, 1));
      }
    }
  }, [selectedDevice, isFullscreen]);

  const handleRefresh = () => {
    setIsLoading(true);
    if (iframeRef.current) {
      iframeRef.current.src = iframeRef.current.src;
    }
    setTimeout(() => setIsLoading(false), 1000);
  };

  const handleOpenInNewTab = () => {
    if (previewUrl) {
      window.open(previewUrl, '_blank');
    }
  };

  const currentDevice = devices.find(d => d.type === selectedDevice) || devices[2];

  return (
    <div className="h-full flex flex-col bg-white rounded-lg border border-slate-200">
      {/* 预览工具栏 */}
      <div className="flex items-center justify-between p-3 border-b border-slate-200">
        <div className="flex items-center space-x-2">
          <Monitor className="w-4 h-4 text-slate-600" />
          <span className="text-sm font-medium text-slate-700">预览</span>
          {isRunning && (
            <span className="text-xs bg-green-600 text-white px-2 py-1 rounded">
              运行中
            </span>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* 视口切换 */}
          <div className="flex items-center space-x-1 bg-slate-100 rounded-lg p-1">
            <button
              onClick={() => setSelectedDevice('desktop')}
              className={`p-1 rounded ${selectedDevice === 'desktop' ? 'bg-blue-600 text-white' : 'text-slate-600 hover:text-slate-800'}`}
            >
              <Monitor className="w-4 h-4" />
            </button>
            <button
              onClick={() => setSelectedDevice('tablet')}
              className={`p-1 rounded ${selectedDevice === 'tablet' ? 'bg-blue-600 text-white' : 'text-slate-600 hover:text-slate-800'}`}
            >
              <Tablet className="w-4 h-4" />
            </button>
            <button
              onClick={() => setSelectedDevice('mobile')}
              className={`p-1 rounded ${selectedDevice === 'mobile' ? 'bg-blue-600 text-white' : 'text-slate-600 hover:text-slate-800'}`}
            >
              <Smartphone className="w-4 h-4" />
            </button>
          </div>

          {/* 操作按钮 */}
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="p-1 text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded disabled:opacity-50"
            title="刷新"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </button>

          <button
            onClick={handleOpenInNewTab}
            className="p-1 text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded"
            title="在新标签页打开"
          >
            <ExternalLink className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* 预览内容 */}
      <div className="flex-1 flex items-center justify-center p-4 bg-slate-50">
        {!isRunning ? (
          <div className="text-center">
            <Monitor className="w-16 h-16 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-600 mb-2">预览未运行</h3>
            <p className="text-slate-500">点击运行按钮开始预览您的应用</p>
          </div>
        ) : isLoading ? (
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-slate-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-slate-600">正在加载预览...</p>
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <div
              style={{
                width: currentDevice.width * scale,
                height: currentDevice.height * scale
              }}
              className="bg-white rounded-lg shadow-lg overflow-hidden"
            >
              <iframe
                ref={iframeRef}
                srcDoc={`
                  <!DOCTYPE html>
                  <html>
                  <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>预览</title>
                    <style>
                      body { margin: 0; padding: 20px; font-family: system-ui, sans-serif; }
                      .container { max-width: 600px; margin: 0 auto; text-align: center; }
                      .card { background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                      .button { background: #3b82f6; color: white; padding: 0.5rem 1rem; border: none; border-radius: 4px; cursor: pointer; }
                      .button:hover { background: #2563eb; }
                    </style>
                  </head>
                  <body>
                    <div class="container">
                      <div class="card">
                        <h1>欢迎使用 AI Web IDE</h1>
                        <p>这是您的应用预览</p>
                        <button class="button" onclick="alert('Hello from preview!')">点击测试</button>
                      </div>
                    </div>
                  </body>
                  </html>
                `}
                className="w-full h-full border-0"
                title="预览"
              />
            </div>
          </div>
        )}
      </div>

      {/* 预览底部状态栏 */}
      <div className="px-3 py-2 border-t border-slate-200 bg-slate-50 text-xs text-slate-600">
        <div className="flex items-center justify-between">
          <span>
            {selectedDevice === 'desktop' && '桌面视图'}
            {selectedDevice === 'tablet' && '平板视图 (768px)'}
            {selectedDevice === 'mobile' && '移动视图 (375px)'}
          </span>
          <span>localhost:3000</span>
        </div>
      </div>
    </div>
  );
};

export default PreviewPanel;
